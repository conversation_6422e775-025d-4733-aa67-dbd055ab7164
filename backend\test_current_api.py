"""
测试当前API是否返回35个统计字段
"""

import requests
import json


def test_current_api():
    """测试当前API响应"""
    
    print("🧪 测试当前API响应...")
    
    # 测试任务ID
    task_id = "task_eda817c0c6cc455794f6fb066c86410b"
    api_url = f"http://localhost:8000/api/v1/tasks/{task_id}"
    
    try:
        print(f"📡 请求API: {api_url}")
        response = requests.get(api_url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            # 检查content_stats
            if "data" in data and "result" in data["data"]:
                result = data["data"]["result"]
                if "content_stats" in result:
                    content_stats = result["content_stats"]
                    
                    print(f"✅ API响应成功")
                    print(f"📊 content_stats字段数: {len(content_stats)}")
                    print(f"📋 content_stats字段列表:")
                    
                    for i, field in enumerate(content_stats.keys(), 1):
                        print(f"   {i:2d}. {field}: {content_stats[field]}")
                    
                    # 检查是否包含新增字段
                    new_fields = [
                        'characters_with_spaces', 'line_count', 'heading_count',
                        'font_count', 'spelling_errors', 'formula_count'
                    ]
                    
                    missing_new_fields = [field for field in new_fields if field not in content_stats]
                    
                    if missing_new_fields:
                        print(f"\n❌ 缺少新增字段: {missing_new_fields}")
                        print("🔍 这表明修改没有生效，可能是：")
                        print("   1. 这是旧的缓存数据")
                        print("   2. Word COM接口没有读取到新字段")
                        print("   3. 数据库中存储的是旧数据")
                    else:
                        print(f"\n✅ 包含所有新增字段")
                    
                    # 检查期望的35个字段
                    expected_fields = [
                        # 学术论文必需统计
                        'page_count', 'word_count', 'character_count', 'characters_with_spaces',
                        'paragraph_count', 'table_count', 'image_count', 'line_count',
                        
                        # 结构分析统计
                        'heading_count', 'section_count', 'footnote_count', 'endnote_count',
                        'reference_count', 'hyperlink_count', 'bookmark_count', 'comment_count', 'field_count',
                        
                        # 格式规范统计
                        'font_count', 'style_count', 'fonts_used', 'styles_used',
                        'page_orientation', 'page_size', 'margin_info', 'line_spacing_info',
                        
                        # 质量检查统计
                        'spelling_errors', 'grammar_errors', 'revision_count', 'version_count',
                        'track_changes_count', 'formula_count', 'equation_count', 'textbox_count',
                        'chart_count', 'drawing_count'
                    ]
                    
                    missing_fields = [field for field in expected_fields if field not in content_stats]
                    
                    print(f"\n📊 完整性检查:")
                    print(f"   期望字段数: {len(expected_fields)}")
                    print(f"   实际字段数: {len(content_stats)}")
                    print(f"   缺少字段数: {len(missing_fields)}")
                    
                    if missing_fields:
                        print(f"   缺少的字段: {missing_fields}")
                    
                    if len(content_stats) == len(expected_fields) and not missing_fields:
                        print("🎉 API返回完整的35个统计字段！")
                        return True
                    else:
                        print("❌ API返回的统计字段不完整")
                        return False
                        
                else:
                    print("❌ API响应中没有content_stats字段")
                    return False
            else:
                print("❌ API响应结构不正确")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


def test_api_health():
    """测试API健康状态"""
    
    print("\n🔍 测试API健康状态...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ API服务正常运行")
            return True
        else:
            print(f"❌ API健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API服务无法访问: {str(e)}")
        return False


if __name__ == "__main__":
    print("🧪 测试当前API统计字段")
    print("=" * 50)
    
    # 先测试API健康状态
    if test_api_health():
        # 再测试具体的统计字段
        success = test_current_api()
        
        if success:
            print("\n🎉 测试通过：API返回完整统计字段")
        else:
            print("\n❌ 测试失败：需要进一步调试")
            print("\n🔧 建议的解决方案:")
            print("   1. 重新上传文档触发新的分析")
            print("   2. 检查Word COM接口是否正确读取")
            print("   3. 检查数据库中的数据是否更新")
    else:
        print("\n❌ API服务不可用，请检查后端服务状态")
