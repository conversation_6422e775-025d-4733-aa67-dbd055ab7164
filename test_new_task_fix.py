#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import json
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.document_processor import DocumentProcessor
from app.core.resource_manager import WordInstancePool

def test_new_task_fix():
    """测试新任务的参考文献修复是否生效"""
    print("🔍 测试新任务的参考文献修复")
    print("=" * 50)
    
    # 初始化资源管理器
    resource_manager = WordInstancePool()
    
    try:
        # 使用上下文管理器获取Word实例
        with resource_manager.get_instance() as word_instance:
            # 打开文档
            doc_path = r"D:\Works\paper-check-win\docs\test.docx"
            doc = word_instance.open_document(doc_path)
            
            # 创建文档处理器
            processor = DocumentProcessor(resource_manager)
            
            # 完整的文档处理流程
            print("🔄 开始完整的文档处理流程...")
            result = processor.analyze_document_comprehensive(doc_path, include_formatting=False)
            
            print(f"✅ 文档处理完成")
            
            # 查找参考文献结构
            document_structures = result.get('document_structures', [])
            
            reference_structure = None
            for structure in document_structures:
                if structure.get('name') == '参考文献':
                    reference_structure = structure
                    break
            
            if reference_structure:
                print(f"\n📊 参考文献结构详细信息:")
                print(f"  - 结构名称: {reference_structure.get('name', '')}")
                print(f"  - 页码: {reference_structure.get('page', '')}")
                print(f"  - 字数: {reference_structure.get('word_count', 0)}")
                print(f"  - 参考文献条数: {reference_structure.get('reference_count', 0)}")
                
                # 检查是否有详细的中文/外文统计
                if 'reference_chinese_count' in reference_structure:
                    print(f"  - 中文参考文献: {reference_structure.get('reference_chinese_count', 0)}条")
                    print(f"  - 外文参考文献: {reference_structure.get('reference_foreign_count', 0)}条")
                    print(f"  ✅ 包含中文/外文统计字段")
                else:
                    print(f"  ❌ 缺少中文/外文统计字段")
                
                if 'reference_display' in reference_structure:
                    print(f"  - 显示文本: {reference_structure.get('reference_display', '')}")
                    print(f"  ✅ 包含显示文本字段")
                else:
                    print(f"  ❌ 缺少显示文本字段")
                
                # 验证结果
                total_refs = reference_structure.get('reference_count', 0)
                chinese_refs = reference_structure.get('reference_chinese_count', 0)
                foreign_refs = reference_structure.get('reference_foreign_count', 0)
                display_text = reference_structure.get('reference_display', '')
                
                print(f"\n🔍 验证结果:")
                print(f"总条数: {total_refs}")
                print(f"中文: {chinese_refs}条")
                print(f"外文: {foreign_refs}条")
                print(f"显示: {display_text}")
                
                # 验证结果
                if total_refs == 14:
                    print("✅ 总条数正确（14条）")
                else:
                    print(f"❌ 总条数不正确，期望14条，实际{total_refs}条")
                
                if chinese_refs > 0 and foreign_refs > 0:
                    print("✅ 中文/外文分类成功")
                    if display_text == f"中文{chinese_refs}条;外文{foreign_refs}条":
                        print("✅ 显示文本格式正确")
                    else:
                        print(f"❌ 显示文本格式不正确，期望'中文{chinese_refs}条;外文{foreign_refs}条'，实际'{display_text}'")
                else:
                    print(f"❌ 中文/外文分类失败")
                
                # 保存结果到文件以便检查
                output_file = "backend/result_new_task.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                print(f"\n💾 结果已保存到: {output_file}")
                
            else:
                print("❌ 未找到参考文献结构")
            
            # 关闭文档
            word_instance.close_document(doc)
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        resource_manager.shutdown()

if __name__ == "__main__":
    test_new_task_fix()
