"""
测试API结构深度优化效果
"""

import json
import asyncio
from app.tasks.manager import TaskManager


async def test_deep_optimization():
    """测试API结构深度优化效果"""
    
    print("🧪 测试API结构深度优化...")
    
    # 创建任务管理器实例
    task_manager = TaskManager()
    
    # 模拟包含重复数据的原始结果
    mock_result_with_duplicates = {
        "task_type": "paper_check",
        "status": "completed",
        "compliance_score": 87.5,
        "problems_found": 2,
        "processing_time": 80.5,
        
        # 模拟重复的document_info
        "document_info": {
            "pages": 36,  # 重复1
            "words": 18806,  # 重复1
            "title": "新媒体技术对舞蹈编导创作手法的影响研究",  # 重复1
            "author": "李岩",  # 重复1
            "cover_page_info": {
                "title": "新媒体技术对舞蹈编导创作手法的影响研究",  # 重复2
                "author": "李岩",  # 重复2
                "major": "舞蹈编导（专升本）",
                "department": "艺术学院",
                "student_id": "32219350130",
                "advisor": "展烨",
                "date": "2025年5月20日",
                "degree_type": "学士学位论文",
                "raw_text": "超长的原始文本数据..." * 100  # 冗余数据
            }
        },
        
        # 模拟analysis_result包装层
        "analysis_result": {
            "content_stats": {
                "page_count": 36,  # 重复2
                "word_count": 18806,  # 重复2
                "table_count": 10,
                "image_count": 9,
                "paragraph_count": 514,
                "character_count": 22630,
                "formula_count": 0,
                "reference_count": 0,
                "footnote_count": 0
            }
        },
        
        "document_structures": [
            {
                "name": "封面",
                "type": "standard",
                "status": "present",
                "page": 1
            }
        ],
        
        "outline": [
            {
                "text": "封面",
                "level": 1,
                "page": 1
            }
        ],
        
        "detection_standard": "hbkj_bachelor_2024",
        "standard_name": "河北科技学院学士论文检测标准 (2024版)",
        
        # 冗余字段
        "formatted_json": None,
        "check_result": None,
        
        "check_summary": {
            "compliance_score": 87.5,
            "total_problems": 2,
            "major_problems": 1,
            "minor_problems": 1
        },
        
        "analysis_summary": {
            "document_type": "report",
            "quality_score": 85,
            "hierarchy_score": 90,
            "completeness_score": 88
        },
        
        "processing_meta": {
            "extraction_method": "word_com_pool",
            "processing_pipeline": "optimized_v2",
            "processing_times": {
                "raw_extraction": 79.5,
                "preprocessing": 0.004,
                "structure_analysis": 0.003
            }
        }
    }
    
    print("📊 原始数据分析:")
    original_json = json.dumps(mock_result_with_duplicates, ensure_ascii=False)
    original_size = len(original_json)
    
    # 统计重复数据
    title_count = original_json.count('"title"')
    author_count = original_json.count('"author"')
    pages_count = original_json.count('"pages"') + original_json.count('"page_count"')
    words_count = original_json.count('"words"') + original_json.count('"word_count"')
    
    print(f"   原始数据大小: {original_size} 字符")
    print(f"   title字段出现: {title_count} 次")
    print(f"   author字段出现: {author_count} 次")
    print(f"   页数字段出现: {pages_count} 次")
    print(f"   字数字段出现: {words_count} 次")
    print(f"   包含analysis_result包装层: {'analysis_result' in mock_result_with_duplicates}")
    print(f"   包含冗余字段formatted_json: {'formatted_json' in mock_result_with_duplicates}")
    
    # 测试优化转换
    print("\n🔧 测试深度优化转换...")
    optimized_result = task_manager._convert_dict_to_frontend_format(mock_result_with_duplicates)
    
    print("📊 优化后数据分析:")
    optimized_json = json.dumps(optimized_result, ensure_ascii=False)
    optimized_size = len(optimized_json)
    
    # 统计优化后的数据
    opt_title_count = optimized_json.count('"title"')
    opt_author_count = optimized_json.count('"author"')
    opt_pages_count = optimized_json.count('"pages"') + optimized_json.count('"page_count"')
    opt_words_count = optimized_json.count('"words"') + optimized_json.count('"word_count"')
    
    print(f"   优化后数据大小: {optimized_size} 字符")
    print(f"   title字段出现: {opt_title_count} 次")
    print(f"   author字段出现: {opt_author_count} 次")
    print(f"   页数字段出现: {opt_pages_count} 次")
    print(f"   字数字段出现: {opt_words_count} 次")
    print(f"   包含analysis_result包装层: {'analysis_result' in optimized_result}")
    print(f"   包含冗余字段formatted_json: {'formatted_json' in optimized_result}")
    
    # 计算优化效果
    size_reduction = (1 - optimized_size / original_size) * 100
    print(f"\n📈 优化效果:")
    print(f"   数据大小减少: {size_reduction:.1f}%")
    print(f"   title重复减少: {title_count - opt_title_count} 个")
    print(f"   author重复减少: {author_count - opt_author_count} 个")
    
    # 验证关键优化
    print("\n✅ 验证关键优化:")
    
    # 1. 验证content_stats提升到顶级
    assert 'content_stats' in optimized_result, "❌ content_stats未提升到顶级"
    assert optimized_result['content_stats']['table_count'] == 10, "❌ 表格数不正确"
    assert optimized_result['content_stats']['image_count'] == 9, "❌ 图片数不正确"
    print("   ✅ content_stats成功提升到顶级")
    
    # 2. 验证移除重复数据
    document_info = optimized_result['document_info']
    assert 'pages' not in document_info, "❌ document_info中仍有重复的pages"
    assert 'words' not in document_info, "❌ document_info中仍有重复的words"
    assert 'cover_page_info' not in document_info, "❌ document_info中仍有cover_page_info"
    print("   ✅ 成功移除重复数据")
    
    # 3. 验证精简的document_info
    assert document_info['title'] == "新媒体技术对舞蹈编导创作手法的影响研究", "❌ title不正确"
    assert document_info['author'] == "李岩", "❌ author不正确"
    assert document_info['major'] == "舞蹈编导（专升本）", "❌ major不正确"
    print("   ✅ document_info结构精简且正确")
    
    # 4. 验证移除冗余字段
    assert 'formatted_json' not in optimized_result, "❌ 仍包含formatted_json冗余字段"
    assert 'check_result' not in optimized_result, "❌ 仍包含null的check_result字段"
    print("   ✅ 成功移除冗余字段")
    
    # 5. 验证数据完整性
    assert optimized_result['content_stats']['page_count'] == 36, "❌ 页数数据丢失"
    assert optimized_result['content_stats']['word_count'] == 18806, "❌ 字数数据丢失"
    assert len(optimized_result['document_structures']) == 1, "❌ 文档结构数据丢失"
    print("   ✅ 数据完整性保持良好")
    
    print("\n🎯 模拟前端数据提取:")
    
    # 模拟前端提取统计数据
    content_stats = optimized_result['content_stats']
    frontend_stats = {
        "pages": content_stats['page_count'],
        "words": content_stats['word_count'],
        "tables": content_stats['table_count'],
        "images": content_stats['image_count'],
        "paragraphs": content_stats['paragraph_count']
    }
    
    print(f"   📈 统计数据提取:")
    print(f"      页数: {frontend_stats['pages']}")
    print(f"      字数: {frontend_stats['words']}")
    print(f"      表格数: {frontend_stats['tables']}")
    print(f"      图片数: {frontend_stats['images']}")
    print(f"      段落数: {frontend_stats['paragraphs']}")
    
    # 模拟前端提取文档信息
    doc_info = optimized_result['document_info']
    frontend_doc_info = {
        "title": doc_info['title'],
        "author": doc_info['author'],
        "major": doc_info['major'],
        "standard": optimized_result['standard_name']
    }
    
    print(f"   📋 文档信息提取:")
    print(f"      标题: {frontend_doc_info['title']}")
    print(f"      作者: {frontend_doc_info['author']}")
    print(f"      专业: {frontend_doc_info['major']}")
    print(f"      标准: {frontend_doc_info['standard']}")
    
    print("\n✅ 所有测试通过！")
    print("🎉 API结构深度优化成功！")
    
    print(f"\n📊 最终优化总结:")
    print(f"   ✅ 数据大小减少 {size_reduction:.1f}%")
    print(f"   ✅ 移除了 {title_count - opt_title_count} 个重复title")
    print(f"   ✅ 移除了 {author_count - opt_author_count} 个重复author")
    print(f"   ✅ content_stats提升到顶级")
    print(f"   ✅ 移除了analysis_result包装层")
    print(f"   ✅ 清理了冗余字段")
    print(f"   ✅ 保持了数据完整性")


if __name__ == "__main__":
    asyncio.run(test_deep_optimization())
