"""
测试standard_name重复问题修复
"""

import json
import asyncio
from app.tasks.manager import <PERSON>Manager


async def test_standard_name_deduplication():
    """测试standard_name去重效果"""
    
    print("🧪 测试standard_name重复问题修复...")
    
    # 创建任务管理器实例
    task_manager = TaskManager()
    
    # 模拟包含重复standard_name的原始数据
    mock_result_with_duplicates = {
        "task_type": "paper_check",
        "status": "completed",
        "compliance_score": 87.5,
        "problems_found": 2,
        "processing_time": 2.5,
        
        "content_stats": {
            "page_count": 36,
            "word_count": 18806,
            "table_count": 10,
            "image_count": 9
        },
        
        "document_structures": [],
        "outline": [],
        
        "detection_standard": "hbkj_bachelor_2024",
        "standard_name": "河北科技学院学士论文检测标准 (2024版)",  # 第1个
        
        "document_info": {
            "pages": 36,
            "words": 18806,
            "title": "测试文档",
            "author": "测试作者"
        }
    }
    
    print("📊 原始数据中的standard_name:")
    json_str = json.dumps(mock_result_with_duplicates, ensure_ascii=False)
    standard_name_count = json_str.count('"standard_name"')
    print(f"   发现 {standard_name_count} 个 standard_name 字段")
    
    # 测试转换函数
    print("\n🔧 测试_convert_dict_to_frontend_format...")
    converted_result = task_manager._convert_dict_to_frontend_format(mock_result_with_duplicates)
    
    print("📊 转换后的结果:")
    converted_json_str = json.dumps(converted_result, ensure_ascii=False)
    converted_standard_name_count = converted_json_str.count('"standard_name"')
    print(f"   转换后有 {converted_standard_name_count} 个 standard_name 字段")
    
    # 验证只有一个standard_name
    assert converted_standard_name_count == 1, f"❌ 仍有{converted_standard_name_count}个standard_name，应该只有1个"
    
    # 验证standard_name的值
    if 'standard_name' in converted_result:
        print(f"   standard_name值: {converted_result['standard_name']}")
        assert converted_result['standard_name'] == "河北科技学院学士论文检测标准 (2024版)", "❌ standard_name值不正确"
    else:
        raise AssertionError("❌ 转换后缺少standard_name字段")
    
    print("\n🎯 模拟API响应结构:")
    
    # 模拟完整的API响应
    api_response = {
        "success": True,
        "code": 200,
        "message": "获取任务信息成功",
        "data": {
            "task_id": "task_test123",
            "filename": "test.docx",
            "file_size": 2560385,
            "analysis_options": {
                "detection_standard": "hbkj_bachelor_2024"
                # 🔥 注意：这里不再包含standard_name，避免重复
            },
            "result": converted_result,
            # 🔥 注意：顶级也不再包含standard_name，避免重复
            "created_at": "2025-07-26T16:36:21.755181+00:00",
            "status": "completed"
        }
    }
    
    # 验证整个API响应中的standard_name数量
    api_json_str = json.dumps(api_response, ensure_ascii=False)
    api_standard_name_count = api_json_str.count('"standard_name"')
    print(f"   完整API响应中有 {api_standard_name_count} 个 standard_name 字段")
    
    # 验证只有一个standard_name（在result中）
    assert api_standard_name_count == 1, f"❌ API响应中有{api_standard_name_count}个standard_name，应该只有1个"
    
    # 验证standard_name的位置
    result_has_standard_name = 'standard_name' in api_response['data']['result']
    analysis_options_has_standard_name = 'standard_name' in api_response['data']['analysis_options']
    top_level_has_standard_name = 'standard_name' in api_response['data']
    
    print(f"   result中有standard_name: {result_has_standard_name}")
    print(f"   analysis_options中有standard_name: {analysis_options_has_standard_name}")
    print(f"   顶级有standard_name: {top_level_has_standard_name}")
    
    assert result_has_standard_name, "❌ result中应该有standard_name"
    assert not analysis_options_has_standard_name, "❌ analysis_options中不应该有standard_name"
    assert not top_level_has_standard_name, "❌ 顶级不应该有standard_name"
    
    print("\n🎯 模拟前端数据提取:")
    
    # 模拟前端从API响应中提取standard_name
    task_result = api_response['data']['result']
    analysis_options = api_response['data']['analysis_options']
    
    # DocumentDetail.vue的提取逻辑
    def get_detection_standard():
        # 优先从result中获取
        if task_result.get('standard_name'):
            return task_result['standard_name']
        # 兼容：从analysis_options获取（但这里已经没有了）
        if analysis_options.get('standard_name'):
            return analysis_options['standard_name']
        return '标准检测'
    
    # StatisticsReport.vue的提取逻辑
    def get_standard_for_report():
        return task_result.get('standard_name') or analysis_options.get('standard_name') or '标准检测'
    
    document_detail_standard = get_detection_standard()
    statistics_report_standard = get_standard_for_report()
    
    print(f"   DocumentDetail.vue获取到: {document_detail_standard}")
    print(f"   StatisticsReport.vue获取到: {statistics_report_standard}")
    
    # 验证前端能正确获取standard_name
    expected_standard = "河北科技学院学士论文检测标准 (2024版)"
    assert document_detail_standard == expected_standard, f"❌ DocumentDetail获取错误: {document_detail_standard}"
    assert statistics_report_standard == expected_standard, f"❌ StatisticsReport获取错误: {statistics_report_standard}"
    
    print("\n✅ 所有测试通过！")
    print("🎉 standard_name重复问题修复成功！")
    
    print("\n📊 修复效果总结:")
    print("   ✅ 移除了analysis_options中的重复standard_name")
    print("   ✅ 移除了顶级的重复standard_name")
    print("   ✅ 只在result中保留一个standard_name")
    print("   ✅ 前端能正确获取唯一的standard_name")
    print("   ✅ API响应结构清晰，无重复数据")


if __name__ == "__main__":
    asyncio.run(test_standard_name_deduplication())
