#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

# 模拟从Word表格中提取的文本（包含控制字符）
test_texts = [
    "[1]高绿苑.\"沉浸式\"舞蹈剧场中观演关系研究[D].山东师范大学,2023.\r\x07",
    "[2]廖伟.舞台表演视角下新媒体舞蹈的发展策略研究[D].华侨大学,2023.\r\x07",
    "[3]郭佩祎,毕思文.论新媒体技术对当代舞蹈创作与传播的影响[J].尚舞,2023,(08):126-128.\r\x07",
    "[4]<PERSON>.Requardt & Rosenberg: Super Normal Extra Natural[J].The Stage,2025,(10):19.\r\x07"
]

print("🔍 测试参考文献检测逻辑")
print("=" * 50)

for i, text in enumerate(test_texts, 1):
    print(f"\n测试文本 {i}: {repr(text)}")
    
    # 原始文本
    print(f"原始文本: {text}")
    
    # 清理后的文本
    cleaned_text = re.sub(r'[\r\x07\x0c]+', ' ', text)
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
    print(f"清理后: {cleaned_text}")
    
    # 测试正则表达式
    pattern1 = r'\[\d+\][^[\]]*\[[A-Za-z]\]'
    pattern2 = r'\[\d+\][^[\]]{10,}'
    
    match1 = re.search(pattern1, cleaned_text)
    match2 = re.search(pattern2, cleaned_text)
    
    print(f"模式1匹配: {bool(match1)} - {match1.group() if match1 else 'None'}")
    print(f"模式2匹配: {bool(match2)} - {match2.group() if match2 else 'None'}")
    
    # 测试是否会被计数
    if re.search(pattern1, cleaned_text) or re.search(pattern2, cleaned_text):
        print("✅ 会被计数")
    else:
        print("❌ 不会被计数")
