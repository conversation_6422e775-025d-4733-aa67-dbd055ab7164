<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端字数统计测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端字数统计修复测试</h1>
        <p>测试前端页面是否正确显示修复后的字数统计数据</p>

        <div class="test-section">
            <div class="test-title">🔧 测试步骤</div>
            <ol>
                <li>点击下面的按钮测试API连接</li>
                <li>检查字数统计数据是否正确显示</li>
                <li>验证封面页字数是否从6字增加到61字</li>
                <li>确认其他结构的字数统计是否合理</li>
            </ol>
        </div>

        <div class="test-section">
            <div class="test-title">🌐 API测试</div>
            <button class="btn" onclick="testTaskAPI()">测试任务详情API</button>
            <button class="btn" onclick="testStructureAPI()">测试结构统计API</button>
            <button class="btn" onclick="openStatisticsPage()">打开统计报告页面</button>
            <div id="api-results"></div>
        </div>

        <div class="test-section">
            <div class="test-title">📊 期望的字数统计结果</div>
            <table>
                <thead>
                    <tr>
                        <th>结构名称</th>
                        <th>修复前字数</th>
                        <th>修复后期望字数</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>封面</td>
                        <td>6字</td>
                        <td>61字</td>
                        <td>应包含整个封面页的所有文字</td>
                    </tr>
                    <tr>
                        <td>任务书</td>
                        <td>较少</td>
                        <td>58字</td>
                        <td>任务书标题及相关内容</td>
                    </tr>
                    <tr>
                        <td>中文摘要</td>
                        <td>较少</td>
                        <td>360字</td>
                        <td>摘要内容的完整字数</td>
                    </tr>
                    <tr>
                        <td>正文</td>
                        <td>较少</td>
                        <td>657字</td>
                        <td>正文开始部分的字数</td>
                    </tr>
                    <tr>
                        <td>参考文献</td>
                        <td>较少</td>
                        <td>313字，15条</td>
                        <td>参考文献的字数和条数</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <div class="test-title">✅ 验证清单</div>
            <div id="validation-checklist">
                <label><input type="checkbox" id="check1"> 前端页面能正常加载</label><br>
                <label><input type="checkbox" id="check2"> API返回正确的document_structures数据</label><br>
                <label><input type="checkbox" id="check3"> 封面页字数显示为61字（而不是6字）</label><br>
                <label><input type="checkbox" id="check4"> 其他结构字数统计合理</label><br>
                <label><input type="checkbox" id="check5"> "当前情况"列显示实际字数</label><br>
                <label><input type="checkbox" id="check6"> 参考文献显示条数而不是字数</label><br>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        const TASK_ID = 'task_cbec6ee040c7434daf0746f053b08286';

        async function testTaskAPI() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<div class="info">正在测试任务详情API...</div>';

            try {
                const response = await fetch(`${API_BASE}/tasks/${TASK_ID}`);
                const data = await response.json();

                if (response.ok) {
                    const documentStructures = data.result?.document_structures || [];
                    
                    let html = '<div class="success">✅ 任务详情API测试成功</div>';
                    html += `<div class="info">找到 ${documentStructures.length} 个文档结构</div>`;
                    
                    if (documentStructures.length > 0) {
                        html += '<table><thead><tr><th>结构名称</th><th>字数</th><th>参考文献条数</th><th>状态</th></tr></thead><tbody>';
                        
                        documentStructures.forEach(structure => {
                            const name = structure.name || structure.structure_name || '未知';
                            const wordCount = structure.word_count || 0;
                            const refCount = structure.reference_count || 0;
                            const status = structure.status || 'unknown';
                            
                            html += `<tr>
                                <td>${name}</td>
                                <td>${wordCount}字</td>
                                <td>${refCount}条</td>
                                <td>${status}</td>
                            </tr>`;
                        });
                        
                        html += '</tbody></table>';
                        
                        // 检查封面页字数
                        const coverStructure = documentStructures.find(s => 
                            (s.name || s.structure_name || '').includes('封面')
                        );
                        
                        if (coverStructure) {
                            const coverWordCount = coverStructure.word_count || 0;
                            if (coverWordCount >= 60) {
                                html += '<div class="success">🎉 封面页字数修复成功！显示为 ' + coverWordCount + ' 字</div>';
                            } else {
                                html += '<div class="error">❌ 封面页字数仍然偏低：' + coverWordCount + ' 字</div>';
                            }
                        }
                    }
                    
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `<div class="error">❌ API请求失败: ${data.message || response.statusText}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        async function testStructureAPI() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<div class="info">正在测试结构统计API...</div>';

            try {
                const response = await fetch(`${API_BASE}/tasks/${TASK_ID}/structure-stats`);
                const data = await response.json();

                if (response.ok) {
                    let html = '<div class="success">✅ 结构统计API测试成功</div>';
                    html += `<div class="info">总结构数: ${data.total_structures || 0}</div>`;
                    html += `<div class="info">总字数: ${data.total_words || 0}</div>`;
                    
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `<div class="error">❌ 结构统计API请求失败: ${data.message || response.statusText}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        function openStatisticsPage() {
            const url = `http://localhost:5173/statistics/${TASK_ID}`;
            window.open(url, '_blank');
        }

        // 页面加载时自动测试
        window.onload = function() {
            setTimeout(testTaskAPI, 1000);
        };
    </script>
</body>
</html>
