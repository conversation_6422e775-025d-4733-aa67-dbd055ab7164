#!/usr/bin/env python3
"""
调试参考文献结构识别过程
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.document_processor import DocumentProcessor
import structlog

# 配置日志
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

def debug_reference_structure():
    """调试参考文献结构识别"""
    
    print("🔍 调试参考文献结构识别过程")
    print("=" * 50)
    
    # 创建文档处理器
    processor = DocumentProcessor()
    
    # 分析文档
    file_path = r"D:\Works\paper-check-win\docs\test.docx"
    print(f"📄 分析文档: {file_path}")
    
    try:
        result = processor.analyze_document_comprehensive(file_path)
        
        if result and 'structures' in result:
            structures = result['structures']
            print(f"\n📊 找到 {len(structures)} 个结构")
            
            # 查找所有包含"参考文献"的结构
            reference_structures = []
            for i, structure in enumerate(structures):
                structure_name = structure.get('name', '')
                if '参考文献' in structure_name:
                    reference_structures.append((i, structure))
                    
            print(f"\n🔍 找到 {len(reference_structures)} 个参考文献相关结构:")
            
            for i, (index, structure) in enumerate(reference_structures):
                print(f"\n参考文献结构 #{i+1} (索引 {index}):")
                print(f"  - 名称: {structure.get('name', '')}")
                print(f"  - 页码: {structure.get('page', '')}")
                print(f"  - 类型: {structure.get('type', '')}")
                print(f"  - 状态: {structure.get('status', '')}")
                print(f"  - 字数: {structure.get('word_count', 0)}")
                print(f"  - 参考文献条数: {structure.get('reference_count', 0)}")
                
                # 检查内容
                content = structure.get('content', {})
                text = content.get('text', '')
                
                print(f"  - 内容长度: {len(text)} 字符")
                print(f"  - 内容预览: {text[:100]}...")
                
                # 检查是否有详细统计
                if 'reference_chinese_count' in structure:
                    print(f"  - 中文参考文献: {structure.get('reference_chinese_count', 0)}条")
                    print(f"  - 外文参考文献: {structure.get('reference_foreign_count', 0)}条")
                    print(f"  - 显示文本: {structure.get('reference_display', '')}")
                
                # 分析内容中的参考文献
                if text:
                    print(f"\n  📝 内容分析:")
                    lines = [line.strip() for line in text.split('\n') if line.strip()]
                    print(f"    - 总行数: {len(lines)}")
                    
                    import re
                    reference_patterns = [
                        r'^\s*\[\d+\]',  # [1] 格式
                        r'^\s*\d+\.\s*',  # 1. 格式
                        r'^\s*\(\d+\)',   # (1) 格式
                    ]
                    
                    reference_lines = []
                    for line in lines:
                        for pattern in reference_patterns:
                            if re.match(pattern, line):
                                reference_lines.append(line)
                                break
                    
                    print(f"    - 参考文献格式行数: {len(reference_lines)}")
                    
                    if reference_lines:
                        print(f"    - 前3条参考文献:")
                        for j, ref_line in enumerate(reference_lines[:3]):
                            print(f"      [{j+1}] {ref_line[:80]}...")
                    
                    # 手动测试统计
                    chinese_count, foreign_count = processor._count_references_by_language(text)
                    print(f"    - 手动统计: 中文{chinese_count}条, 外文{foreign_count}条")
            
            # 检查是否有其他页面包含参考文献内容
            print(f"\n🔍 检查其他页面是否包含参考文献内容:")
            
            # 获取页面内容
            if 'pages_content' in result:
                pages_content = result['pages_content']
                
                for page_num, page_data in pages_content.items():
                    if isinstance(page_data, list):
                        page_text = ""
                        reference_count = 0
                        
                        for paragraph in page_data:
                            text = paragraph.get('text', '')
                            page_text += text + "\n"
                            
                            # 检查是否包含参考文献格式
                            import re
                            if re.search(r'\[\d+\].*[D|J|M]\]', text):
                                reference_count += 1
                        
                        if reference_count > 0:
                            print(f"  第{page_num}页: 发现 {reference_count} 条可能的参考文献")
                            
                            # 显示前几条
                            lines = [line.strip() for line in page_text.split('\n') if line.strip()]
                            ref_lines = []
                            for line in lines:
                                if re.search(r'\[\d+\]', line):
                                    ref_lines.append(line)
                            
                            if ref_lines:
                                print(f"    前3条:")
                                for j, ref_line in enumerate(ref_lines[:3]):
                                    print(f"      {ref_line[:80]}...")
        
        else:
            print("❌ 文档处理失败或未返回结构数据")
            
    except Exception as e:
        print(f"❌ 处理过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_reference_structure()
