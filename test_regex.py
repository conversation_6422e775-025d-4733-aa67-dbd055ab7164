#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

# 测试参考文献条目
test_references = [
    "[1]高绿苑.\"沉浸式\"舞蹈剧场中观演关系研究[D].山东师范大学,2023.",
    "[2]廖伟.舞台表演视角下新媒体舞蹈的发展策略研究[D].华侨大学,2023.",
    "[3]郭佩祎,毕思文.论新媒体技术对当代舞蹈创作与传播的影响[J].尚舞,2023,(08):126-128.",
    "[4]<PERSON>.Requardt & Rosenberg: Super Normal Extra Natural[J].The Stage,2025,(10):19."
]

# 测试不同的正则表达式
patterns = [
    r'\[\d+\][^[\]]*\[[A-Za-z]\]',  # 当前使用的模式
    r'\[\d+\].*\[[DJMCNRSPAZ]\]',   # 原始模式
    r'\[\d+\].*\[[A-Za-z]\]',       # 简化模式
    r'\[\d+\][^[\]]{10,}',          # 备用模式
]

print("🔍 测试参考文献正则表达式匹配")
print("=" * 50)

for i, pattern in enumerate(patterns, 1):
    print(f"\n模式 {i}: {pattern}")
    print("-" * 30)
    
    for ref in test_references:
        match = re.search(pattern, ref)
        if match:
            print(f"✅ 匹配: {ref[:50]}...")
        else:
            print(f"❌ 不匹配: {ref[:50]}...")
