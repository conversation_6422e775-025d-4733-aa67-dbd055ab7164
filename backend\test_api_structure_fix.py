"""
测试API结构修复效果
"""

import json
import asyncio
from app.tasks.manager import TaskManager


async def test_api_structure_fix():
    """测试API结构修复效果"""
    
    print("🧪 测试API结构修复...")
    
    # 创建任务管理器实例
    task_manager = TaskManager()
    
    # 模拟原始数据（包含content_stats）
    mock_result = {
        "task_type": "paper_check",
        "status": "completed",
        "compliance_score": 87.5,
        "problems_found": 2,
        "processing_time": 2.5,
        
        # 🔥 新结构：content_stats在顶级
        "content_stats": {
            "page_count": 36,
            "word_count": 18806,
            "table_count": 10,
            "image_count": 9,
            "paragraph_count": 514,
            "character_count": 22630,
            "formula_count": 0,
            "reference_count": 0,
            "footnote_count": 0
        },
        
        "document_structures": [
            {
                "name": "封面",
                "type": "standard",
                "status": "present",
                "page": 1
            }
        ],
        
        "outline": [
            {
                "text": "封面",
                "level": 1,
                "page": 1
            }
        ],
        
        "check_summary": {
            "compliance_score": 87.5,
            "total_problems": 2,
            "major_problems": 1,
            "minor_problems": 1
        },
        
        "detection_standard": "hbkj_bachelor_2024",
        "standard_name": "河北科技学院学士论文检测标准 (2024版)",
        
        "document_info": {
            "pages": 36,
            "words": 18806,
            "title": "测试文档标题",
            "author": "测试作者"
        }
    }
    
    print("📊 原始结果结构:")
    print(f"   content_stats在顶级: {'content_stats' in mock_result}")
    print(f"   analysis_result存在: {'analysis_result' in mock_result}")
    print(f"   表格数: {mock_result['content_stats']['table_count']}")
    print(f"   图片数: {mock_result['content_stats']['image_count']}")
    
    # 测试转换函数
    print("\n🔧 测试_convert_dict_to_frontend_format...")
    converted_result = task_manager._convert_dict_to_frontend_format(mock_result)
    
    print("📊 转换后结果结构:")
    print(f"   content_stats在顶级: {'content_stats' in converted_result}")
    print(f"   analysis_result存在: {'analysis_result' in converted_result}")
    
    if 'content_stats' in converted_result:
        content_stats = converted_result['content_stats']
        print(f"   表格数: {content_stats.get('table_count', 0)}")
        print(f"   图片数: {content_stats.get('image_count', 0)}")
        print(f"   段落数: {content_stats.get('paragraph_count', 0)}")
    
    # 验证关键修复
    assert 'content_stats' in converted_result, "❌ content_stats字段缺失"
    assert converted_result['content_stats']['table_count'] == 10, f"❌ 表格数错误: {converted_result['content_stats']['table_count']}"
    assert converted_result['content_stats']['image_count'] == 9, f"❌ 图片数错误: {converted_result['content_stats']['image_count']}"
    assert converted_result['content_stats']['paragraph_count'] == 514, f"❌ 段落数错误: {converted_result['content_stats']['paragraph_count']}"
    
    # 验证没有重新创建analysis_result包装层
    if 'analysis_result' in converted_result:
        print("⚠️ 警告：仍然存在analysis_result包装层")
    else:
        print("✅ 成功移除analysis_result包装层")
    
    print("\n🎯 模拟前端数据提取:")
    
    # 模拟StatisticsReport.vue的数据提取
    statistics_data = {
        "pages": converted_result['content_stats']['page_count'],
        "words": converted_result['content_stats']['word_count'],
        "tables": converted_result['content_stats']['table_count'],
        "images": converted_result['content_stats']['image_count'],
        "paragraphs": converted_result['content_stats']['paragraph_count']
    }
    
    print(f"📈 StatisticsReport.vue 数据提取:")
    print(f"   页数: {statistics_data['pages']}")
    print(f"   字数: {statistics_data['words']}")
    print(f"   表格数: {statistics_data['tables']} (应该是10)")
    print(f"   图片数: {statistics_data['images']} (应该是9)")
    print(f"   段落数: {statistics_data['paragraphs']} (应该是514)")
    
    # 模拟DocumentDetail.vue的数据提取
    document_stats = {
        "pages": converted_result['content_stats']['page_count'],
        "words": converted_result['content_stats']['word_count'],
        "tables": converted_result['content_stats']['table_count'],
        "images": converted_result['content_stats']['image_count']
    }
    
    print(f"📋 DocumentDetail.vue 数据提取:")
    print(f"   页数: {document_stats['pages']}")
    print(f"   字数: {document_stats['words']}")
    print(f"   表格数: {document_stats['tables']} (应该是10)")
    print(f"   图片数: {document_stats['images']} (应该是9)")
    
    # 验证前端能正确获取数据
    assert statistics_data['tables'] == 10, f"❌ 前端表格数获取错误: {statistics_data['tables']}"
    assert statistics_data['images'] == 9, f"❌ 前端图片数获取错误: {statistics_data['images']}"
    assert statistics_data['paragraphs'] == 514, f"❌ 前端段落数获取错误: {statistics_data['paragraphs']}"
    
    print("\n✅ 所有测试通过！")
    print("🎉 API结构修复成功！")
    
    print("\n📊 修复效果总结:")
    print("   ✅ content_stats保持在顶级")
    print("   ✅ 移除了analysis_result包装层")
    print("   ✅ 表格数正确显示为10")
    print("   ✅ 图片数正确显示为9")
    print("   ✅ 段落数正确显示为514")
    print("   ✅ 前端能正确提取所有统计数据")


if __name__ == "__main__":
    asyncio.run(test_api_structure_fix())
