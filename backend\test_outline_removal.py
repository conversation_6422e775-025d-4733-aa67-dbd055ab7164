"""
测试outline移除和document_structures增强效果
"""

import json
import asyncio
from app.tasks.manager import TaskManager


async def test_outline_removal():
    """测试outline移除效果"""
    
    print("🧪 测试outline移除和document_structures增强...")
    
    # 创建任务管理器实例
    task_manager = TaskManager()
    
    # 模拟包含outline和document_structures的原始结果
    mock_result_with_outline = {
        "task_type": "paper_check",
        "status": "completed",
        "compliance_score": 87.5,
        "problems_found": 2,
        "processing_time": 80.5,
        
        "content_stats": {
            "page_count": 36,
            "word_count": 18806,
            "table_count": 10,
            "image_count": 9,
            "paragraph_count": 514
        },
        
        # 模拟document_structures（增强后应包含level字段）
        "document_structures": [
            {
                "name": "封面",
                "page": 1,
                "type": "standard",
                "status": "present",
                "level": 0,  # 🔥 新增level字段
                "content": {
                    "text": "学士学位论文",
                    "style": "正文",
                    "paragraph_index": 1,
                    "level": 0,
                    "structure_name": "封面"
                },
                "required": True,
                "word_count": 6,
                "reference_count": 0
            },
            {
                "name": "任务书",
                "page": 2,
                "type": "standard",
                "status": "present",
                "level": 1,  # 🔥 新增level字段
                "content": {
                    "text": "河北科技学院本科生毕业设计（论文）任务书",
                    "style": "任务书-标题",
                    "paragraph_index": 0,
                    "level": 1,
                    "structure_name": "任务书"
                },
                "required": True,
                "word_count": 18,
                "reference_count": 0
            },
            {
                "name": "测试非标准结构",
                "page": 7,
                "type": "non_standard",
                "status": "present",
                "level": 2,  # 🔥 新增level字段
                "content": {
                    "text": "河北科技学院本科生毕业设计（论文）测试",
                    "style": "Normal",
                    "paragraph_index": 5,
                    "level": 2,
                    "structure_name": "测试非标准结构"
                },
                "required": False,
                "word_count": 17,
                "reference_count": 0
            }
        ],
        
        # 模拟重复的outline（应该被移除）
        "outline": [
            {
                "page": 1,
                "text": "学士学位论文",
                "type": "standard",
                "level": 0,
                "structure_name": "封面"
            },
            {
                "page": 2,
                "text": "河北科技学院本科生毕业设计（论文）任务书",
                "type": "standard",
                "level": 1,
                "structure_name": "任务书"
            },
            {
                "page": 7,
                "text": "河北科技学院本科生毕业设计（论文）测试",
                "type": "non_standard",
                "level": 2,
                "structure_name": "测试非标准结构"
            }
        ],
        
        "detection_standard": "hbkj_bachelor_2024",
        "standard_name": "河北科技学院学士论文检测标准 (2024版)",
        
        "check_summary": {
            "compliance_score": 87.5,
            "total_problems": 2,
            "major_problems": 1,
            "minor_problems": 1
        }
    }
    
    print("📊 原始数据分析:")
    original_json = json.dumps(mock_result_with_outline, ensure_ascii=False)
    original_size = len(original_json)
    
    # 统计重复数据
    outline_count = original_json.count('"outline"')
    document_structures_count = original_json.count('"document_structures"')
    
    print(f"   原始数据大小: {original_size} 字符")
    print(f"   包含outline字段: {outline_count} 次")
    print(f"   包含document_structures字段: {document_structures_count} 次")
    print(f"   outline条目数: {len(mock_result_with_outline.get('outline', []))}")
    print(f"   document_structures条目数: {len(mock_result_with_outline.get('document_structures', []))}")
    
    # 测试优化转换
    print("\n🔧 测试outline移除转换...")
    optimized_result = task_manager._convert_dict_to_frontend_format(mock_result_with_outline)
    
    print("📊 优化后数据分析:")
    optimized_json = json.dumps(optimized_result, ensure_ascii=False)
    optimized_size = len(optimized_json)
    
    # 统计优化后的数据
    opt_outline_count = optimized_json.count('"outline"')
    opt_document_structures_count = optimized_json.count('"document_structures"')
    
    print(f"   优化后数据大小: {optimized_size} 字符")
    print(f"   包含outline字段: {opt_outline_count} 次")
    print(f"   包含document_structures字段: {opt_document_structures_count} 次")
    print(f"   document_structures条目数: {len(optimized_result.get('document_structures', []))}")
    
    # 计算优化效果
    size_reduction = (1 - optimized_size / original_size) * 100
    print(f"\n📈 优化效果:")
    print(f"   数据大小减少: {size_reduction:.1f}%")
    print(f"   outline字段移除: {outline_count - opt_outline_count} 个")
    
    # 验证关键优化
    print("\n✅ 验证关键优化:")
    
    # 1. 验证outline被移除
    assert 'outline' not in optimized_result, "❌ outline字段未被移除"
    print("   ✅ outline字段成功移除")
    
    # 2. 验证document_structures保留且增强
    assert 'document_structures' in optimized_result, "❌ document_structures字段缺失"
    document_structures = optimized_result['document_structures']
    assert len(document_structures) == 3, f"❌ document_structures条目数错误: {len(document_structures)}"
    print("   ✅ document_structures字段保留且完整")
    
    # 3. 验证document_structures包含level字段
    for i, structure in enumerate(document_structures):
        assert 'level' in structure, f"❌ document_structures[{i}]缺少level字段"
        assert 'content' in structure, f"❌ document_structures[{i}]缺少content字段"
        content = structure['content']
        assert 'level' in content, f"❌ document_structures[{i}].content缺少level字段"
        assert 'structure_name' in content, f"❌ document_structures[{i}].content缺少structure_name字段"
    print("   ✅ document_structures包含所有必要字段")
    
    # 4. 验证数据完整性
    assert document_structures[0]['name'] == "封面", "❌ 第一个结构名称不正确"
    assert document_structures[0]['level'] == 0, "❌ 第一个结构level不正确"
    assert document_structures[1]['name'] == "任务书", "❌ 第二个结构名称不正确"
    assert document_structures[1]['level'] == 1, "❌ 第二个结构level不正确"
    assert document_structures[2]['type'] == "non_standard", "❌ 第三个结构类型不正确"
    assert document_structures[2]['level'] == 2, "❌ 第三个结构level不正确"
    print("   ✅ 数据完整性保持良好")
    
    print("\n🎯 模拟前端数据提取:")
    
    # 模拟前端从document_structures提取结构信息（替代outline）
    frontend_sections = []
    for structure in document_structures:
        section = {
            "name": structure['name'],
            "type": structure['type'],
            "level": structure['level'],
            "page": structure['page'],
            "status": structure['status'],
            "text": structure['content']['text'],
            "style": structure['content']['style']
        }
        frontend_sections.append(section)
    
    print(f"   📋 从document_structures提取的结构信息:")
    for i, section in enumerate(frontend_sections, 1):
        type_icon = "📋" if section['type'] == 'standard' else "🎨"
        print(f"      {i}. {type_icon} L{section['level']} [页{section['page']}] {section['name']}")
    
    # 验证前端能正确提取数据
    assert len(frontend_sections) == 3, f"❌ 前端提取的结构数量错误: {len(frontend_sections)}"
    assert frontend_sections[0]['name'] == "封面", "❌ 前端提取的第一个结构名称错误"
    assert frontend_sections[2]['type'] == "non_standard", "❌ 前端提取的第三个结构类型错误"
    
    print("\n✅ 所有测试通过！")
    print("🎉 outline移除和document_structures增强成功！")
    
    print(f"\n📊 最终优化总结:")
    print(f"   ✅ 数据大小减少 {size_reduction:.1f}%")
    print(f"   ✅ 移除了重复的outline字段")
    print(f"   ✅ 增强了document_structures（添加level等字段）")
    print(f"   ✅ 统一了数据结构，消除重复")
    print(f"   ✅ 前端能正确从document_structures获取所有信息")
    print(f"   ✅ 保持了数据完整性和功能完整性")


if __name__ == "__main__":
    asyncio.run(test_outline_removal())
