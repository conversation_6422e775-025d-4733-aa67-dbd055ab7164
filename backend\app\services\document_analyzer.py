"""
文档分析协调器

负责协调整个文档分析流程，包括：
- 分析流程管理
- 各组件协调
- 结果整合
- 错误处理
- 进度跟踪
"""

import asyncio
import logging
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

from .document_processor import DocumentProcessor, DocumentData
from ..checkers.rule_engine import RuleEngine
from ..models.check_result import CheckResult, CheckSeverity
from ..utils.error_formatter import error_formatter, ErrorCategory, ErrorSeverity as ErrSeverity

logger = logging.getLogger(__name__)


# ==============================================================================
# 检查函数定义 (Check Functions)
# ==============================================================================
# 在此定义所有被规则JSON引用的检查函数。
# 这些函数实现了完整的论文格式检测逻辑，支持结构、内容和格式的全方位检查。

def check_section_order(doc_data: DocumentData, params: Dict[str, Any]) -> CheckResult:
    """
    检查章节顺序。

    根据标准结构定义检查文档的章节是否按正确顺序排列，
    支持识别与校验分离的高级方案。

    Args:
        doc_data: 文档数据
        params: 包含standard_structure的参数

    Returns:
        CheckResult: 检查结果
    """
    rule_id = params.get("rule_id", "structure.section_order")
    rule_name = params.get("rule_name", "章节顺序检查")

    # 获取标准结构定义
    standard_structure = params.get("standard_structure", [])
    if not standard_structure:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=False,
            severity=CheckSeverity.ERROR,
            message="缺少标准结构定义参数"
        )

    # 从文档中识别章节
    identified_sections = _identify_sections(doc_data, standard_structure)

    # 检查必需章节是否存在
    missing_sections = []
    for section_def in standard_structure:
        if section_def.get("required", False):
            section_name = section_def["name"]
            if not any(s["name"] == section_name for s in identified_sections):
                missing_sections.append(section_name)

    # 检查章节顺序
    order_errors = _check_section_order(identified_sections, standard_structure)

    # 生成格式化的错误列表
    formatted_errors = []

    # 添加缺失章节错误
    for section in missing_sections:
        formatted_errors.append({
            "code": "MISSING_SECTION",
            "section_name": section,
            "position": {"section": "document_structure"}
        })

    # 添加顺序错误
    for order_error in order_errors:
        formatted_errors.append({
            "code": "SECTION_ORDER_ERROR",
            "current_section": order_error.split("'")[1] if "'" in order_error else "未知",
            "expected_section": order_error.split("'")[3] if order_error.count("'") >= 4 else "未知",
            "position": {"section": "document_structure"}
        })

    # 使用新的错误格式化系统
    return create_formatted_check_result(
        rule_id=rule_id,
        rule_name=rule_name,
        errors=formatted_errors,
        success_message=f"章节结构检查通过，识别到 {len(identified_sections)} 个章节",
        details={
            "identified_sections": [s["name"] for s in identified_sections],
            "missing_sections": missing_sections,
            "order_errors": order_errors,
            "total_sections_found": len(identified_sections),
            "required_sections_count": len([s for s in standard_structure if s.get("required", False)])
        }
    )

def check_abstract_and_keywords(doc_data: DocumentData, params: Dict[str, Any]) -> CheckResult:
    """
    检查摘要和关键词。

    检查摘要内容的完整性和关键词的数量、格式等。

    Args:
        doc_data: 文档数据
        params: 包含检查参数

    Returns:
        CheckResult: 检查结果
    """
    rule_id = params.get("rule_id", "structure.abstract_and_keywords")
    rule_name = params.get("rule_name", "摘要关键词检查")

    # 查找摘要和关键词部分
    abstract_found = False
    keywords_found = False
    abstract_content = ""
    keywords_content = ""

    if doc_data.elements:
        for i, element in enumerate(doc_data.elements):
            text = element.get("text", "").strip()
            text_lower = text.lower()

            # 识别摘要
            if "摘要" in text and len(text) < 20:  # 摘要标题
                abstract_found = True
                # 查找摘要内容（下一个元素）
                if i + 1 < len(doc_data.elements):
                    abstract_content = doc_data.elements[i + 1].get("text", "")

            # 识别关键词
            if "关键词" in text or "关键字" in text:
                keywords_found = True
                keywords_content = text

    # 检查结果
    errors = []

    if not abstract_found:
        errors.append("未找到摘要部分")
    elif len(abstract_content.strip()) < 50:
        errors.append("摘要内容过短，应包含研究目的、方法、结果和结论")

    if not keywords_found:
        errors.append("未找到关键词部分")
    else:
        # 检查关键词数量
        keyword_count = _count_keywords(keywords_content)
        min_keywords = params.get("min_keywords", 3)
        max_keywords = params.get("max_keywords", 8)

        if keyword_count < min_keywords:
            errors.append(f"关键词数量不足，应至少包含{min_keywords}个关键词，当前{keyword_count}个")
        elif keyword_count > max_keywords:
            errors.append(f"关键词数量过多，应不超过{max_keywords}个关键词，当前{keyword_count}个")

    if errors:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=False,
            severity=CheckSeverity.ERROR,
            message=f"摘要关键词检查失败：{'; '.join(errors)}",
            details={
                "abstract_found": abstract_found,
                "keywords_found": keywords_found,
                "abstract_length": len(abstract_content),
                "keyword_count": _count_keywords(keywords_content) if keywords_found else 0
            }
        )
    else:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=True,
            severity=CheckSeverity.INFO,
            message="摘要关键词检查通过",
            details={
                "abstract_length": len(abstract_content),
                "keyword_count": _count_keywords(keywords_content)
            }
        )

def check_content_length(doc_data: DocumentData, params: Dict[str, Any]) -> CheckResult:
    """
    检查内容长度（字数、条目数等）。

    支持字数统计、条目统计等多种统计方式。

    Args:
        doc_data: 文档数据
        params: 包含min、max、unit、errorMessage等参数

    Returns:
        CheckResult: 检查结果
    """
    rule_id = params.get("rule_id", "content.length_check")
    rule_name = params.get("rule_name", "内容长度检查")

    min_count = params.get("min")
    max_count = params.get("max")
    unit = params.get("unit", "字")
    error_message_template = params.get("errorMessage", "内容长度不符合要求")

    # 根据单位进行不同的统计
    if unit == "字":
        current_count = _count_characters(doc_data)
    elif unit == "条":
        current_count = _count_items(doc_data, params)
    elif unit == "页":
        current_count = doc_data.doc_info.get("page_count", 0)
    else:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=False,
            severity=CheckSeverity.ERROR,
            message=f"不支持的统计单位：{unit}"
        )

    # 检查是否符合要求
    passed = True
    error_details = []

    if min_count is not None and current_count < min_count:
        passed = False
        error_details.append(f"少于最小要求 {min_count}{unit}")

    if max_count is not None and current_count > max_count:
        passed = False
        error_details.append(f"超过最大限制 {max_count}{unit}")

    # 格式化错误消息
    if not passed:
        try:
            message = error_message_template.format(
                min=min_count,
                max=max_count,
                current_count=current_count,
                unit=unit
            )
        except (KeyError, ValueError):
            # 如果模板格式化失败，使用默认格式
            message = f"{error_message_template}。当前：{current_count}{unit}"
    else:
        message = f"内容长度检查通过。当前：{current_count}{unit}"

    return CheckResult(
        rule_id=rule_id,
        rule_name=rule_name,
        passed=passed,
        severity=CheckSeverity.ERROR if not passed else CheckSeverity.INFO,
        message=message,
        details={
            "current_count": current_count,
            "min_count": min_count,
            "max_count": max_count,
            "unit": unit
        }
    )

def check_page_setup(doc_data: DocumentData, params: Dict[str, Any]) -> CheckResult:
    """
    检查页面设置。

    检查页面大小、边距、页眉页脚等设置是否符合要求。
    增强版本支持更详细的页面属性验证。

    Args:
        doc_data: 文档数据
        params: 包含页面设置要求的参数

    Returns:
        CheckResult: 检查结果
    """
    rule_id = params.get("rule_id", "format.page_setup")
    rule_name = params.get("rule_name", "页面设置检查")

    # 获取页面设置要求
    required_setup = params.get("page_setup", {})

    # 从文档信息中获取页面设置
    doc_page_setup = doc_data.doc_info.get("page_setup", {})

    # 如果没有要求，进行基础检查
    if not required_setup:
        basic_issues = _check_basic_page_setup(doc_page_setup)
        if basic_issues:
            return CheckResult(
                rule_id=rule_id,
                rule_name=rule_name,
                passed=False,
                severity=CheckSeverity.WARNING,
                message=f"页面设置存在问题：{'; '.join(basic_issues)}",
                details={"basic_issues": basic_issues, "actual_setup": doc_page_setup}
            )
        else:
            return CheckResult(
                rule_id=rule_id,
                rule_name=rule_name,
                passed=True,
                severity=CheckSeverity.INFO,
                message="页面设置检查通过（基础检查）",
                details={"actual_setup": doc_page_setup}
            )

    # 详细检查页面设置
    setup_errors = []
    warnings = []

    for setting_key, requirement in required_setup.items():
        if isinstance(requirement, dict):
            required_value = requirement.get("value")
            tolerance = requirement.get("tolerance")
            error_message = requirement.get("errorMessage", f"{setting_key}设置不符合要求")
            warning_only = requirement.get("warning_only", False)
        else:
            required_value = requirement
            tolerance = None
            error_message = f"{setting_key}设置不符合要求"
            warning_only = False

        actual_value = doc_page_setup.get(setting_key)

        if actual_value is None:
            message = f"无法获取{setting_key}设置信息"
            if warning_only:
                warnings.append(message)
            else:
                setup_errors.append(message)
            continue

        # 检查值是否匹配
        is_match = _compare_page_setting_values(actual_value, required_value, tolerance)

        if not is_match:
            message = f"{error_message}，期望：{required_value}，实际：{actual_value}"
            if warning_only:
                warnings.append(message)
            else:
                setup_errors.append(message)

    # 生成检查结果
    if setup_errors:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=False,
            severity=CheckSeverity.ERROR,
            message=f"页面设置检查失败：{'; '.join(setup_errors[:3])}{'...' if len(setup_errors) > 3 else ''}",
            details={
                "required_setup": required_setup,
                "actual_setup": doc_page_setup,
                "setup_errors": setup_errors,
                "warnings": warnings
            }
        )
    elif warnings:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=True,
            severity=CheckSeverity.WARNING,
            message=f"页面设置检查通过，但有建议：{'; '.join(warnings[:2])}{'...' if len(warnings) > 2 else ''}",
            details={
                "checked_settings": list(required_setup.keys()),
                "warnings": warnings,
                "actual_setup": doc_page_setup
            }
        )
    else:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=True,
            severity=CheckSeverity.INFO,
            message="页面设置检查通过",
            details={
                "checked_settings": list(required_setup.keys()),
                "actual_setup": doc_page_setup
            }
        )

def check_text_format(doc_data: DocumentData, params: Dict[str, Any]) -> CheckResult:
    """
    检查正文格式。

    检查正文段落的字体、字号、行距、首行缩进等格式是否符合要求。

    Args:
        doc_data: 文档数据
        params: 包含style等参数

    Returns:
        CheckResult: 检查结果
    """
    rule_id = params.get("rule_id", "format.body_text")
    rule_name = params.get("rule_name", "正文格式检查")

    # 获取检查参数
    required_style = params.get("style", {})

    # 查找正文段落（排除标题、摘要等特殊段落）
    body_paragraphs = _find_body_paragraphs(doc_data)

    if not body_paragraphs:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=False,
            severity=CheckSeverity.WARNING,
            message="未找到正文段落"
        )

    # 检查格式
    format_errors = []
    checked_count = 0

    # 抽样检查（避免检查过多段落）
    sample_paragraphs = body_paragraphs[:10] if len(body_paragraphs) > 10 else body_paragraphs

    for paragraph in sample_paragraphs:
        style_errors = _check_element_style(paragraph, required_style)
        if style_errors:
            checked_count += 1
            text_preview = paragraph["text"][:15] + "..." if len(paragraph["text"]) > 15 else paragraph["text"]
            format_errors.extend([f"段落 '{text_preview}': {error}" for error in style_errors])

    # 生成检查结果
    if format_errors:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=False,
            severity=CheckSeverity.ERROR,
            message=f"正文格式检查失败：{'; '.join(format_errors[:3])}{'...' if len(format_errors) > 3 else ''}",
            details={
                "total_paragraphs": len(body_paragraphs),
                "checked_paragraphs": len(sample_paragraphs),
                "format_errors": format_errors
            }
        )
    else:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=True,
            severity=CheckSeverity.INFO,
            message=f"正文格式检查通过，检查了{len(sample_paragraphs)}个段落",
            details={
                "total_paragraphs": len(body_paragraphs),
                "checked_paragraphs": len(sample_paragraphs)
            }
        )

def check_headings_by_level(doc_data: DocumentData, params: Dict[str, Any]) -> CheckResult:
    """
    检查各级标题格式。

    检查标题的字体、字号、对齐方式、加粗等格式是否符合要求。

    Args:
        doc_data: 文档数据
        params: 包含style、pattern、level等参数

    Returns:
        CheckResult: 检查结果
    """
    rule_id = params.get("rule_id", "format.headings")
    rule_name = params.get("rule_name", "标题格式检查")

    # 获取检查参数
    required_style = params.get("style", {})
    pattern = params.get("pattern")
    target_level = params.get("level", 1)  # 默认检查一级标题

    # 查找指定级别的标题
    headings = _find_headings_by_level(doc_data, target_level)

    if not headings:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=False,
            severity=CheckSeverity.WARNING,
            message=f"未找到{target_level}级标题"
        )

    # 检查每个标题
    format_errors = []
    pattern_errors = []

    for heading in headings:
        # 检查格式
        style_errors = _check_element_style(heading, required_style)
        if style_errors:
            format_errors.extend([f"标题 '{heading['text']}': {error}" for error in style_errors])

        # 检查模式匹配
        if pattern:
            import re
            if not re.match(pattern, heading["text"]):
                pattern_errors.append(f"标题 '{heading['text']}' 不符合格式要求")

    # 生成检查结果
    all_errors = format_errors + pattern_errors

    if all_errors:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=False,
            severity=CheckSeverity.ERROR,
            message=f"{target_level}级标题格式检查失败：{'; '.join(all_errors[:3])}{'...' if len(all_errors) > 3 else ''}",
            details={
                "checked_headings": len(headings),
                "format_errors": format_errors,
                "pattern_errors": pattern_errors
            }
        )
    else:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=True,
            severity=CheckSeverity.INFO,
            message=f"{target_level}级标题格式检查通过，检查了{len(headings)}个标题",
            details={
                "checked_headings": len(headings)
            }
        )

def check_header_footer_format(doc_data: DocumentData, params: Dict[str, Any]) -> CheckResult:
    """
    检查页眉页脚格式。

    检查页眉页脚的内容、字体、对齐方式等是否符合要求。

    Args:
        doc_data: 文档数据
        params: 包含页眉页脚要求的参数

    Returns:
        CheckResult: 检查结果
    """
    rule_id = params.get("rule_id", "format.header_footer")
    rule_name = params.get("rule_name", "页眉页脚格式检查")

    # 获取页眉页脚要求
    header_requirements = params.get("header", {})
    footer_requirements = params.get("footer", {})

    # 从文档信息中获取页眉页脚信息（如果有的话）
    doc_header = doc_data.doc_info.get("header", {})
    doc_footer = doc_data.doc_info.get("footer", {})

    format_errors = []

    # 检查页眉
    if header_requirements:
        header_errors = _check_header_footer_section(doc_header, header_requirements, "页眉")
        format_errors.extend(header_errors)

    # 检查页脚
    if footer_requirements:
        footer_errors = _check_header_footer_section(doc_footer, footer_requirements, "页脚")
        format_errors.extend(footer_errors)

    # 如果没有具体要求，返回通过
    if not header_requirements and not footer_requirements:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=True,
            severity=CheckSeverity.INFO,
            message="页眉页脚格式检查通过（无具体要求）"
        )

    # 生成检查结果
    if format_errors:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=False,
            severity=CheckSeverity.ERROR,
            message=f"页眉页脚格式检查失败：{'; '.join(format_errors)}",
            details={
                "header_requirements": header_requirements,
                "footer_requirements": footer_requirements,
                "format_errors": format_errors
            }
        )
    else:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=True,
            severity=CheckSeverity.INFO,
            message="页眉页脚格式检查通过"
        )


def _check_header_footer_section(actual_section: Dict[str, Any],
                                requirements: Dict[str, Any],
                                section_name: str) -> List[str]:
    """
    检查页眉或页脚部分。

    Args:
        actual_section: 实际的页眉/页脚信息
        requirements: 要求
        section_name: 部分名称（页眉/页脚）

    Returns:
        错误列表
    """
    errors = []

    for req_key, requirement in requirements.items():
        if isinstance(requirement, dict):
            required_value = requirement.get("value")
            error_message = requirement.get("errorMessage", f"{section_name}{req_key}不符合要求")
        else:
            required_value = requirement
            error_message = f"{section_name}{req_key}不符合要求"

        actual_value = actual_section.get(req_key)

        if actual_value is None:
            errors.append(f"无法获取{section_name}{req_key}信息")
        elif str(actual_value) != str(required_value):
            errors.append(f"{error_message}，期望：{required_value}，实际：{actual_value}")

    return errors


def check_figures_and_tables(doc_data: DocumentData, params: Dict[str, Any]) -> CheckResult:
    """
    检查图片和表格的格式。

    检查图片和表格的标题、编号、位置等是否符合要求。

    Args:
        doc_data: 文档数据
        params: 包含图表要求的参数

    Returns:
        CheckResult: 检查结果
    """
    rule_id = params.get("rule_id", "format.figures_tables")
    rule_name = params.get("rule_name", "图表格式检查")

    # 查找图片和表格
    figures = _find_figures(doc_data)
    tables = _find_tables(doc_data)

    format_errors = []
    warnings = []

    # 检查图片
    if figures:
        figure_errors = _check_figures_format(figures, params.get("figure_requirements", {}))
        format_errors.extend(figure_errors)
    elif params.get("require_figures", False):
        warnings.append("文档中未找到图片")

    # 检查表格
    if tables:
        table_errors = _check_tables_format(tables, params.get("table_requirements", {}))
        format_errors.extend(table_errors)
    elif params.get("require_tables", False):
        warnings.append("文档中未找到表格")

    # 检查编号连续性
    if figures:
        numbering_errors = _check_figure_numbering(figures)
        format_errors.extend(numbering_errors)

    if tables:
        numbering_errors = _check_table_numbering(tables)
        format_errors.extend(numbering_errors)

    # 生成检查结果
    if format_errors:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=False,
            severity=CheckSeverity.ERROR,
            message=f"图表格式检查失败：{'; '.join(format_errors[:3])}{'...' if len(format_errors) > 3 else ''}",
            details={
                "figures_count": len(figures),
                "tables_count": len(tables),
                "format_errors": format_errors,
                "warnings": warnings
            }
        )
    elif warnings:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=True,
            severity=CheckSeverity.WARNING,
            message=f"图表格式检查通过，但有建议：{'; '.join(warnings)}",
            details={
                "figures_count": len(figures),
                "tables_count": len(tables),
                "warnings": warnings
            }
        )
    else:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=True,
            severity=CheckSeverity.INFO,
            message=f"图表格式检查通过，检查了{len(figures)}个图片和{len(tables)}个表格",
            details={
                "figures_count": len(figures),
                "tables_count": len(tables)
            }
        )

def check_references_format(doc_data: DocumentData, params: Dict[str, Any]) -> CheckResult:
    """
    检查参考文献格式。

    检查参考文献的编号格式、内容格式等是否符合要求。

    Args:
        doc_data: 文档数据
        params: 包含格式要求的参数

    Returns:
        CheckResult: 检查结果
    """
    rule_id = params.get("rule_id", "format.references")
    rule_name = params.get("rule_name", "参考文献格式检查")

    # 查找参考文献部分
    references = _find_references(doc_data)

    if not references:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=False,
            severity=CheckSeverity.WARNING,
            message="未找到参考文献部分"
        )

    # 检查参考文献格式
    format_errors = []
    numbering_errors = []

    expected_number = 1
    for ref in references:
        text = ref.get("text", "").strip()

        # 检查编号格式
        import re
        number_match = re.match(r'^\[(\d+)\]', text)
        if not number_match:
            numbering_errors.append(f"参考文献缺少正确的编号格式：{text[:30]}...")
        else:
            actual_number = int(number_match.group(1))
            if actual_number != expected_number:
                numbering_errors.append(f"参考文献编号不连续，期望[{expected_number}]，实际[{actual_number}]")
            expected_number = actual_number + 1

        # 检查内容格式（基本检查）
        if len(text) < 20:
            format_errors.append(f"参考文献内容过短：{text}")
        elif not any(char in text for char in ['.', '，', ',']):
            format_errors.append(f"参考文献格式可能不正确：{text[:50]}...")

    # 生成检查结果
    all_errors = numbering_errors + format_errors

    if all_errors:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=False,
            severity=CheckSeverity.ERROR,
            message=f"参考文献格式检查失败：{'; '.join(all_errors[:3])}{'...' if len(all_errors) > 3 else ''}",
            details={
                "total_references": len(references),
                "numbering_errors": numbering_errors,
                "format_errors": format_errors
            }
        )
    else:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=True,
            severity=CheckSeverity.INFO,
            message=f"参考文献格式检查通过，检查了{len(references)}条文献",
            details={
                "total_references": len(references)
            }
        )

def check_paragraph_format(doc_data: DocumentData, params: Dict[str, Any]) -> CheckResult:
    """
    检查特定段落格式（如英文摘要标题）。

    检查特定段落的字体、字号、对齐方式等格式是否符合要求。

    Args:
        doc_data: 文档数据
        params: 包含style、exact_text、pattern等参数

    Returns:
        CheckResult: 检查结果
    """
    rule_id = params.get("rule_id", "format.paragraph")
    rule_name = params.get("rule_name", "特定段落格式检查")

    # 获取检查参数
    required_style = params.get("style", {})
    exact_text = params.get("exact_text")
    pattern = params.get("pattern")

    # 查找目标段落
    target_paragraphs = []

    if doc_data.elements:
        for element in doc_data.elements:
            if element.get("type") != "paragraph":
                continue

            text = element.get("text", "").strip()

            # 精确文本匹配
            if exact_text and text == exact_text:
                target_paragraphs.append(element)
            # 模式匹配
            elif pattern:
                import re
                if re.search(pattern, text):
                    target_paragraphs.append(element)

    if not target_paragraphs:
        search_criteria = exact_text or pattern or "未指定"
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=False,
            severity=CheckSeverity.WARNING,
            message=f"未找到匹配的段落：{search_criteria}"
        )

    # 检查每个目标段落的格式
    format_errors = []

    for paragraph in target_paragraphs:
        style_errors = _check_element_style(paragraph, required_style)
        if style_errors:
            text_preview = paragraph["text"][:20] + "..." if len(paragraph["text"]) > 20 else paragraph["text"]
            format_errors.extend([f"段落 '{text_preview}': {error}" for error in style_errors])

    # 生成检查结果
    if format_errors:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=False,
            severity=CheckSeverity.ERROR,
            message=f"段落格式检查失败：{'; '.join(format_errors[:2])}{'...' if len(format_errors) > 2 else ''}",
            details={
                "checked_paragraphs": len(target_paragraphs),
                "format_errors": format_errors
            }
        )
    else:
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=True,
            severity=CheckSeverity.INFO,
            message=f"段落格式检查通过，检查了{len(target_paragraphs)}个段落",
            details={
                "checked_paragraphs": len(target_paragraphs)
            }
        )


# 辅助函数
# ==============================================================================

def create_formatted_check_result(rule_id: str, rule_name: str,
                                 errors: List[Dict[str, Any]] = None,
                                 warnings: List[Dict[str, Any]] = None,
                                 success_message: str = None,
                                 details: Dict[str, Any] = None) -> CheckResult:
    """
    创建格式化的检查结果。

    Args:
        rule_id: 规则ID
        rule_name: 规则名称
        errors: 错误列表
        warnings: 警告列表
        success_message: 成功消息
        details: 详细信息

    Returns:
        CheckResult: 格式化的检查结果
    """
    errors = errors or []
    warnings = warnings or []
    details = details or {}

    # 格式化错误和警告
    formatted_errors = error_formatter.format_multiple_errors(errors)
    formatted_warnings = error_formatter.format_multiple_errors(warnings)

    all_issues = formatted_errors + formatted_warnings

    if formatted_errors:
        # 有错误
        severity = CheckSeverity.CRITICAL if any(e.severity == ErrSeverity.CRITICAL for e in formatted_errors) else CheckSeverity.ERROR
        message = error_formatter.create_summary_message(all_issues)
        passed = False
    elif formatted_warnings:
        # 只有警告
        severity = CheckSeverity.WARNING
        message = error_formatter.create_summary_message(all_issues)
        passed = True
    else:
        # 无问题
        severity = CheckSeverity.INFO
        message = success_message or f"{rule_name}检查通过"
        passed = True

    # 添加详细报告到details
    if all_issues:
        details["detailed_report"] = error_formatter.create_detailed_report(all_issues)
        details["error_count"] = len(formatted_errors)
        details["warning_count"] = len(formatted_warnings)

    return CheckResult(
        rule_id=rule_id,
        rule_name=rule_name,
        passed=passed,
        severity=severity,
        message=message,
        details=details
    )

def _identify_sections(doc_data: DocumentData, standard_structure: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    从文档中识别章节。

    Args:
        doc_data: 文档数据
        standard_structure: 标准结构定义

    Returns:
        识别到的章节列表，包含name、position、element_index等信息
    """
    identified_sections = []

    if not doc_data.elements:
        return identified_sections

    for i, element in enumerate(doc_data.elements):
        element_text = element.get("text", "").strip()
        if not element_text:
            continue

        # 对每个标准章节定义进行匹配
        for section_def in standard_structure:
            section_name = section_def["name"]
            identifiers = section_def.get("identifiers", [])

            # 检查是否已经识别过这个章节
            if any(s["name"] == section_name for s in identified_sections):
                continue

            # 使用标识符进行匹配
            if _matches_section_identifiers(element_text, identifiers):
                identified_sections.append({
                    "name": section_name,
                    "position": i,
                    "element_index": i,
                    "text": element_text,
                    "element_type": element.get("type", "unknown")
                })
                break

    # 按在文档中的位置排序
    identified_sections.sort(key=lambda x: x["position"])
    return identified_sections


def _matches_section_identifiers(text: str, identifiers: List[str]) -> bool:
    """
    检查文本是否匹配章节标识符。

    Args:
        text: 要检查的文本
        identifiers: 章节标识符列表

    Returns:
        是否匹配
    """
    text_lower = text.lower()

    for identifier in identifiers:
        identifier_lower = identifier.lower()

        # 精确匹配
        if identifier_lower == text_lower:
            return True

        # 包含匹配
        if identifier_lower in text_lower:
            return True

        # 对于中文章节，检查是否包含关键字
        if len(identifier) > 1 and identifier in text:
            return True

    return False


def _check_section_order(identified_sections: List[Dict[str, Any]],
                        standard_structure: List[Dict[str, Any]]) -> List[str]:
    """
    检查章节顺序是否正确。

    Args:
        identified_sections: 识别到的章节列表
        standard_structure: 标准结构定义

    Returns:
        顺序错误列表
    """
    order_errors = []

    if len(identified_sections) < 2:
        return order_errors

    # 创建标准顺序映射
    standard_order = {section["name"]: i for i, section in enumerate(standard_structure)}

    # 检查相邻章节的顺序
    for i in range(len(identified_sections) - 1):
        current_section = identified_sections[i]["name"]
        next_section = identified_sections[i + 1]["name"]

        current_order = standard_order.get(current_section, -1)
        next_order = standard_order.get(next_section, -1)

        # 如果两个章节都在标准定义中，检查顺序
        if current_order >= 0 and next_order >= 0:
            if current_order > next_order:
                order_errors.append(f"章节 '{next_section}' 应该在 '{current_section}' 之前")

    return order_errors


def _count_characters(doc_data: DocumentData) -> int:
    """
    统计文档中的字符数。

    Args:
        doc_data: 文档数据

    Returns:
        字符数
    """
    total_chars = 0

    if doc_data.elements:
        for element in doc_data.elements:
            text = element.get("text", "")
            # 只统计中文字符和英文单词
            chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
            english_words = len([w for w in text.split() if w.isalpha()])
            total_chars += chinese_chars + english_words

    return total_chars


def _count_items(doc_data: DocumentData, params: Dict[str, Any]) -> int:
    """
    统计条目数量。

    Args:
        doc_data: 文档数据
        params: 参数，可能包含item_pattern等

    Returns:
        条目数量
    """
    import re

    item_pattern = params.get("item_pattern", r"^\[\d+\]")  # 默认匹配 [1] [2] 格式
    total_items = 0

    if doc_data.elements:
        for element in doc_data.elements:
            text = element.get("text", "").strip()
            if re.match(item_pattern, text):
                total_items += 1

    return total_items


def _count_keywords(keywords_text: str) -> int:
    """
    统计关键词数量。

    Args:
        keywords_text: 包含关键词的文本

    Returns:
        关键词数量
    """
    if not keywords_text:
        return 0

    # 移除"关键词："等前缀
    text = keywords_text
    for prefix in ["关键词：", "关键词:", "关键字：", "关键字:", "Keywords:", "keywords:"]:
        if prefix in text:
            text = text.split(prefix, 1)[1]
            break

    # 按分隔符分割关键词
    separators = ["；", ";", "，", ",", "、"]
    keywords = [text]

    for sep in separators:
        new_keywords = []
        for kw in keywords:
            new_keywords.extend(kw.split(sep))
        keywords = new_keywords

    # 清理并统计
    clean_keywords = [kw.strip() for kw in keywords if kw.strip()]
    return len(clean_keywords)


def _find_headings_by_level(doc_data: DocumentData, level: int) -> List[Dict[str, Any]]:
    """
    查找指定级别的标题。

    Args:
        doc_data: 文档数据
        level: 标题级别

    Returns:
        标题列表
    """
    headings = []

    if doc_data.elements:
        for element in doc_data.elements:
            if element.get("type") == "heading" and element.get("level") == level:
                headings.append(element)

    return headings


def _find_body_paragraphs(doc_data: DocumentData) -> List[Dict[str, Any]]:
    """
    查找正文段落（排除标题、摘要等特殊段落）。

    Args:
        doc_data: 文档数据

    Returns:
        正文段落列表
    """
    body_paragraphs = []

    if doc_data.elements:
        for element in doc_data.elements:
            if element.get("type") != "paragraph":
                continue

            text = element.get("text", "").strip()

            # 排除特殊段落
            if _is_special_paragraph(text):
                continue

            body_paragraphs.append(element)

    return body_paragraphs


def _is_special_paragraph(text: str) -> bool:
    """
    判断是否为特殊段落（标题、摘要等）。

    Args:
        text: 段落文本

    Returns:
        是否为特殊段落
    """
    text_lower = text.lower()

    # 特殊段落标识
    special_indicators = [
        "摘要", "abstract", "关键词", "keywords", "目录", "contents",
        "参考文献", "reference", "致谢", "附录", "appendix"
    ]

    for indicator in special_indicators:
        if indicator in text_lower and len(text) < 50:  # 短文本且包含特殊标识
            return True

    # 章节标题模式
    if text.startswith("第") and "章" in text and len(text) < 30:
        return True

    return False


def _check_element_style(element: Dict[str, Any], required_style: Dict[str, Any]) -> List[str]:
    """
    检查元素样式是否符合要求。

    Args:
        element: 文档元素
        required_style: 要求的样式

    Returns:
        错误列表
    """
    errors = []
    element_style = element.get("style", {})

    for style_key, style_requirement in required_style.items():
        if isinstance(style_requirement, dict):
            required_value = style_requirement.get("value")
            tolerance = style_requirement.get("tolerance", 0)
            error_message = style_requirement.get("errorMessage", f"{style_key}格式不符合要求")
        else:
            required_value = style_requirement
            tolerance = 0
            error_message = f"{style_key}格式不符合要求"

        actual_value = element_style.get(style_key)

        if actual_value is None:
            errors.append(f"缺少{style_key}样式")
            continue

        # 数值比较（如字号）
        if isinstance(required_value, (int, float)) and isinstance(actual_value, (int, float)):
            if abs(actual_value - required_value) > tolerance:
                errors.append(f"{error_message}，期望：{required_value}，实际：{actual_value}")
        # 字符串比较
        elif str(actual_value) != str(required_value):
            errors.append(f"{error_message}，期望：{required_value}，实际：{actual_value}")

    return errors


def _find_references(doc_data: DocumentData) -> List[Dict[str, Any]]:
    """
    查找参考文献条目。

    Args:
        doc_data: 文档数据

    Returns:
        参考文献列表
    """
    references = []
    in_references_section = False

    if doc_data.elements:
        for element in doc_data.elements:
            text = element.get("text", "").strip()

            # 检查是否进入参考文献部分
            if "参考文献" in text and len(text) < 20:
                in_references_section = True
                continue

            # 如果在参考文献部分，查找文献条目
            if in_references_section:
                # 检查是否是文献条目（以[数字]开头）
                import re
                if re.match(r'^\[\d+\]', text):
                    references.append(element)
                # 如果遇到新的章节标题，停止查找
                elif text.startswith("第") and "章" in text:
                    break
                elif text in ["致谢", "附录", "谢辞"]:
                    break

    return references


def _check_basic_page_setup(page_setup: Dict[str, Any]) -> List[str]:
    """
    检查基础页面设置。

    Args:
        page_setup: 页面设置信息

    Returns:
        问题列表
    """
    issues = []

    # 检查纸张大小
    paper_size = page_setup.get("paper_size")
    if paper_size and paper_size.upper() not in ["A4", "A3", "LETTER"]:
        issues.append(f"建议使用标准纸张大小，当前：{paper_size}")

    # 检查边距
    margins = ["margin_top", "margin_bottom", "margin_left", "margin_right"]
    for margin in margins:
        value = page_setup.get(margin)
        if value:
            # 简单检查边距是否过小
            if isinstance(value, str) and "cm" in value:
                try:
                    margin_value = float(value.replace("cm", ""))
                    if margin_value < 1.5:
                        issues.append(f"{margin}过小，建议至少1.5cm，当前：{value}")
                except ValueError:
                    pass

    return issues


def _compare_page_setting_values(actual: Any, required: Any, tolerance: Any = None) -> bool:
    """
    比较页面设置值。

    Args:
        actual: 实际值
        required: 要求值
        tolerance: 容差

    Returns:
        是否匹配
    """
    if tolerance is None:
        return str(actual) == str(required)

    # 数值比较（如边距）
    if isinstance(tolerance, (int, float)):
        try:
            actual_num = float(str(actual).replace("cm", "").replace("pt", ""))
            required_num = float(str(required).replace("cm", "").replace("pt", ""))
            return abs(actual_num - required_num) <= tolerance
        except (ValueError, TypeError):
            return str(actual) == str(required)

    return str(actual) == str(required)


def _find_figures(doc_data: DocumentData) -> List[Dict[str, Any]]:
    """
    查找文档中的图片。

    Args:
        doc_data: 文档数据

    Returns:
        图片列表
    """
    figures = []

    if doc_data.elements:
        for element in doc_data.elements:
            if element.get("type") == "image" or element.get("type") == "figure":
                figures.append(element)
            # 查找图片标题
            elif "图" in element.get("text", "") and len(element.get("text", "")) < 50:
                figures.append(element)

    return figures


def _find_tables(doc_data: DocumentData) -> List[Dict[str, Any]]:
    """
    查找文档中的表格。

    Args:
        doc_data: 文档数据

    Returns:
        表格列表
    """
    tables = []

    if doc_data.elements:
        for element in doc_data.elements:
            if element.get("type") == "table":
                tables.append(element)
            # 查找表格标题
            elif "表" in element.get("text", "") and len(element.get("text", "")) < 50:
                tables.append(element)

    return tables


def _check_figures_format(figures: List[Dict[str, Any]], requirements: Dict[str, Any]) -> List[str]:
    """
    检查图片格式。

    Args:
        figures: 图片列表
        requirements: 格式要求

    Returns:
        错误列表
    """
    errors = []

    for i, figure in enumerate(figures):
        # 检查图片标题
        if "title_required" in requirements and requirements["title_required"]:
            if not figure.get("title") and "图" not in figure.get("text", ""):
                errors.append(f"第{i+1}个图片缺少标题")

        # 检查图片大小
        if "max_width" in requirements:
            width = figure.get("width")
            if width and width > requirements["max_width"]:
                errors.append(f"第{i+1}个图片宽度超限：{width} > {requirements['max_width']}")

    return errors


def _check_tables_format(tables: List[Dict[str, Any]], requirements: Dict[str, Any]) -> List[str]:
    """
    检查表格格式。

    Args:
        tables: 表格列表
        requirements: 格式要求

    Returns:
        错误列表
    """
    errors = []

    for i, table in enumerate(tables):
        # 检查表格标题
        if "title_required" in requirements and requirements["title_required"]:
            if not table.get("title") and "表" not in table.get("text", ""):
                errors.append(f"第{i+1}个表格缺少标题")

        # 检查表格列数
        if "max_columns" in requirements:
            columns = table.get("columns", 0)
            if columns > requirements["max_columns"]:
                errors.append(f"第{i+1}个表格列数过多：{columns} > {requirements['max_columns']}")

    return errors


def _check_figure_numbering(figures: List[Dict[str, Any]]) -> List[str]:
    """
    检查图片编号连续性。

    Args:
        figures: 图片列表

    Returns:
        错误列表
    """
    errors = []
    import re

    expected_num = 1
    for figure in figures:
        text = figure.get("text", "")
        match = re.search(r'图\s*(\d+)', text)
        if match:
            actual_num = int(match.group(1))
            if actual_num != expected_num:
                errors.append(f"图片编号不连续，期望图{expected_num}，实际图{actual_num}")
            expected_num = actual_num + 1

    return errors


def _check_table_numbering(tables: List[Dict[str, Any]]) -> List[str]:
    """
    检查表格编号连续性。

    Args:
        tables: 表格列表

    Returns:
        错误列表
    """
    errors = []
    import re

    expected_num = 1
    for table in tables:
        text = table.get("text", "")
        match = re.search(r'表\s*(\d+)', text)
        if match:
            actual_num = int(match.group(1))
            if actual_num != expected_num:
                errors.append(f"表格编号不连续，期望表{expected_num}，实际表{actual_num}")
            expected_num = actual_num + 1

    return errors


# 将函数名映射到可调用对象
CHECK_FUNCTIONS = {
    "check_section_order": check_section_order,
    "check_abstract_and_keywords": check_abstract_and_keywords,
    "check_content_length": check_content_length,
    "check_page_setup": check_page_setup,
    "check_text_format": check_text_format,
    "check_headings_by_level": check_headings_by_level,
    "check_header_footer_format": check_header_footer_format,
    "check_references_format": check_references_format,
    "check_paragraph_format": check_paragraph_format,
    "check_figures_and_tables": check_figures_and_tables,
}


class DocumentAnalysisError(Exception):
    """文档分析异常"""
    pass


@dataclass
class AnalysisResult:
    """分析结果"""
    success: bool = False
    content_analysis: Optional[Any] = None
    processing_time: float = 0.0
    error_message: Optional[str] = None
    warnings: List[str] = field(default_factory=list)
    document_info: Dict[str, Any] = field(default_factory=dict)
    check_results: List[CheckResult] = field(default_factory=list)


class DocumentAnalyzer:
    """文档分析器"""
    
    def __init__(self, rule_file_path: str = "backend/config/rules/hbkj_bachelor_2024.json"):
        """初始化文档分析器"""
        self.document_processor = DocumentProcessor()
        self.rule_engine = RuleEngine(check_functions=CHECK_FUNCTIONS)
        try:
            self.rule_engine.load_rules_from_file(rule_file_path)
        except Exception as e:
            logger.error("在初始化时加载规则引擎失败", exc_info=e)
            # 在这种情况下，引擎将为空，但不会让整个应用崩溃。
            # execute_check会安全地返回一个空列表。
            self.rule_engine = None 
        self._warnings = []
    
    async def analyze_document(self, file_path: str, progress_callback=None) -> AnalysisResult:
        """
        分析文档
        
        Args:
            file_path: 文档文件路径
            
        Returns:
            AnalysisResult: 分析结果
        """
        start_time = datetime.now()
        self._warnings = []
        
        try:
            logger.info(f"开始分析文档: {file_path}")
            
            # 验证文件
            if not Path(file_path).exists():
                raise DocumentAnalysisError(f"文件不存在: {file_path}")
            
            # 获取文档信息
            if progress_callback:
                await progress_callback(28, "正在获取文档信息...")
            doc_info = await self._extract_document_info(file_path)

            # 内容分析（最耗时的步骤）
            if progress_callback:
                await progress_callback(32, "正在分析文档内容和结构...")
            content_analysis_result = await self._analyze_content(file_path)

            if progress_callback:
                await progress_callback(45, "文档分析即将完成...")

            # 准备传递给规则引擎的数据
            document_data = DocumentData(
                file_path=file_path,
                doc_info=doc_info,
                content_stats=getattr(content_analysis_result, 'content_stats', {}),
                # 以下字段需要从 document_processor 获取更详细的数据填充
                elements=[], 
                paragraphs=[],
                tables=[],
                images=[]
            )

            # 调用规则引擎执行检查
            check_results = []
            if self.rule_engine:
                check_results = await self.rule_engine.execute_check(document_data)
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            
            logger.info(f"文档分析完成: {file_path}, 耗时 {processing_time:.2f} 秒")
            
            return AnalysisResult(
                success=True,
                content_analysis=content_analysis_result,
                processing_time=processing_time,
                warnings=self._warnings,
                document_info=doc_info,
                check_results=check_results
            )
                
        except Exception as e:
            error_msg = f"文档分析失败: {e}"
            logger.error(error_msg)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return AnalysisResult(
                success=False,
                error_message=error_msg,
                processing_time=processing_time,
                warnings=self._warnings,
                check_results=[]
            )
    
    async def _extract_document_info(self, file_path: str) -> Dict[str, Any]:
        """提取文档基本信息"""
        try:
            # 1. 获取基本文档信息
            basic_info = await asyncio.get_event_loop().run_in_executor(
                None, self.document_processor.get_document_info, file_path
            )
            
            # 2. 获取文档结构信息（包含封面页信息）
            structure_info = await asyncio.get_event_loop().run_in_executor(
                None, self.document_processor.analyze_document_structure, file_path
            )
            
            # 3. 合并信息，确保封面页信息被包含
            document_info = basic_info.copy()
            if structure_info and 'cover_page_info' in structure_info:
                document_info['cover_page_info'] = structure_info['cover_page_info']
                logger.info(f"成功获取封面页信息: {structure_info['cover_page_info']}")
            
            return document_info
            
        except Exception as e:
            self._add_warning(f"提取文档信息失败: {e}")
            return {}
    
    async def _analyze_content(self, file_path: str) -> Any:
        """分析文档内容（性能优化版本）"""
        import time
        start_time = time.time()
        logger.info(f"🚀 开始综合分析文档内容: {file_path}")

        # 🔥 性能优化：使用新的综合分析方法，一次性完成内容提取和结构分析
        try:
            logger.info(f"🔍 开始异步综合分析: {file_path}")
            analysis_start = time.time()

            # 使用优化的综合分析方法
            loop = asyncio.get_event_loop()
            comprehensive_result = await loop.run_in_executor(
                None,
                lambda: self.document_processor.analyze_document_comprehensive(file_path, True)
            )

            analysis_time = time.time() - analysis_start
            logger.info(f"⏱️ 综合分析耗时: {analysis_time:.2f}秒")
            logger.info(f"🔍 综合分析结果: {type(comprehensive_result)}, 键: {list(comprehensive_result.keys()) if isinstance(comprehensive_result, dict) else 'N/A'}")

            if isinstance(comprehensive_result, dict):
                # 提取内容结果
                content_result = comprehensive_result.get('content', {})

                # 提取结构结果（保持向后兼容）
                structure_result = comprehensive_result.get('structure', comprehensive_result)

                doc_structs = structure_result.get('document_structures', [])
                outline = structure_result.get('outline', [])
                logger.info(f"🔍 结构数据: document_structures={len(doc_structs)}个, outline={len(outline)}个")
            else:
                logger.warning(f"⚠️ 综合分析返回了非字典类型: {comprehensive_result}")
                content_result = {}
                structure_result = None

        except Exception as e:
            logger.error(f"❌ 综合分析失败: {str(e)}")
            import traceback
            logger.error(f"❌ 详细错误: {traceback.format_exc()}")
            # 回退到原来的方法
            logger.info("🔄 回退到分离式分析方法")
            fallback_start = time.time()

            content_result = await asyncio.get_event_loop().run_in_executor(
                None, self.document_processor.extract_document_content, file_path, True
            )
            content_time = time.time() - fallback_start
            logger.info(f"⏱️ 内容提取耗时: {content_time:.2f}秒")

            structure_start = time.time()
            structure_result = await asyncio.get_event_loop().run_in_executor(
                None, self.document_processor.analyze_document_structure, file_path
            )
            structure_time = time.time() - structure_start
            logger.info(f"⏱️ 结构分析耗时: {structure_time:.2f}秒")

        total_time = time.time() - start_time
        logger.info(f"⏱️ _analyze_content 总耗时: {total_time:.2f}秒")

        # 🔥 关键：优先使用结构分析的统计数据（这是准确的）
        content_stats = {}
        document_structures = []
        outline = []
        structure_analysis = {}

        if structure_result and isinstance(structure_result, dict):
            stats = structure_result.get('statistics', {})
            content_stats = {
                # 🔥 学术论文必需统计
                'page_count': stats.get('pages', 0),
                'word_count': stats.get('words', 0),
                'character_count': stats.get('characters', 0),
                'characters_with_spaces': stats.get('characters_with_spaces', 0),
                'paragraph_count': stats.get('paragraphs', 0),
                'table_count': stats.get('tables', 0),
                'image_count': stats.get('images', 0),
                'line_count': stats.get('lines', 0),

                # 🔥 结构分析统计
                'heading_count': stats.get('heading_count', 0),
                'section_count': stats.get('sections', 0),
                'footnote_count': stats.get('footnote_count', 0),
                'endnote_count': stats.get('endnote_count', 0),
                'reference_count': stats.get('reference_count', 0),
                'hyperlink_count': stats.get('hyperlink_count', 0),
                'bookmark_count': stats.get('bookmark_count', 0),
                'comment_count': stats.get('comment_count', 0),
                'field_count': stats.get('field_count', 0),

                # 🔥 格式规范统计
                'font_count': stats.get('font_count', 0),
                'style_count': stats.get('style_count', 0),
                'fonts_used': stats.get('fonts_used', []),
                'styles_used': stats.get('styles_used', []),
                'page_orientation': stats.get('page_orientation', 'unknown'),
                'page_size': stats.get('page_size', 'unknown'),
                'margin_info': stats.get('margin_info', {}),
                'line_spacing_info': stats.get('line_spacing_info', {}),

                # 🔥 质量检查统计
                'spelling_errors': stats.get('spelling_errors', 0),
                'grammar_errors': stats.get('grammar_errors', 0),
                'revision_count': stats.get('revision_count', 0),
                'version_count': stats.get('version_count', 0),
                'track_changes_count': stats.get('track_changes_count', 0),
                'formula_count': stats.get('formula_count', 0),
                'equation_count': stats.get('equation_count', 0),
                'textbox_count': stats.get('textbox_count', 0),
                'chart_count': stats.get('chart_count', 0),
                'drawing_count': stats.get('drawing_count', 0),
            }

            # 🔥 优化：提取文档结构数据，移除重复的outline
            document_structures = structure_result.get('document_structures', [])
            structure_analysis = {
                # 🔥 移除outline，统一使用document_structures
                'sections': structure_result.get('sections', []),
                'styles_used': structure_result.get('styles_used', []),
                'toc_entries': structure_result.get('toc_entries', [])
            }

            logger.info(f"🔍 提取到文档结构: {len(document_structures)} 个结构")
        else:
            logger.warning(f"⚠️ structure_result 为空或不是字典: {structure_result}")
        
        # 如果结构分析失败，从内容结果中估算统计信息
        if not content_stats and content_result and isinstance(content_result, dict):
            paragraphs = content_result.get('paragraphs', [])
            tables = content_result.get('tables', [])
            images = content_result.get('images', [])
            text = content_result.get('text', '')

            content_stats = {
                # 基础统计
                'page_count': 0,
                'word_count': self._count_words(text),
                'character_count': len(text),
                'characters_with_spaces': len(text),
                'paragraph_count': len(paragraphs),
                'table_count': len(tables),
                'image_count': len(images),
                'line_count': text.count('\n') if text else 0,

                # 结构分析统计（估算）
                'heading_count': 0,
                'section_count': 1,
                'footnote_count': 0,
                'endnote_count': 0,
                'reference_count': 0,
                'hyperlink_count': 0,
                'bookmark_count': 0,
                'comment_count': 0,
                'field_count': 0,

                # 格式规范统计（默认值）
                'font_count': 0,
                'style_count': 0,
                'fonts_used': [],
                'styles_used': [],
                'page_orientation': 'unknown',
                'page_size': 'unknown',
                'margin_info': {},
                'line_spacing_info': {},

                # 质量检查统计（默认值）
                'spelling_errors': 0,
                'grammar_errors': 0,
                'revision_count': 0,
                'version_count': 0,
                'track_changes_count': 0,
                'formula_count': 0,
                'equation_count': 0,
                'textbox_count': 0,
                'chart_count': 0,
                'drawing_count': 0,
            }
        
        # 确保有统计信息
        if not content_stats:
            self._add_warning("无法从文档中提取任何统计信息")
            content_stats = {
                # 基础统计
                'page_count': 0,
                'word_count': 0,
                'character_count': 0,
                'characters_with_spaces': 0,
                'paragraph_count': 0,
                'table_count': 0,
                'image_count': 0,
                'line_count': 0,

                # 结构分析统计
                'heading_count': 0,
                'section_count': 0,
                'footnote_count': 0,
                'endnote_count': 0,
                'reference_count': 0,
                'hyperlink_count': 0,
                'bookmark_count': 0,
                'comment_count': 0,
                'field_count': 0,

                # 格式规范统计
                'font_count': 0,
                'style_count': 0,
                'fonts_used': [],
                'styles_used': [],
                'page_orientation': 'unknown',
                'page_size': 'unknown',
                'margin_info': {},
                'line_spacing_info': {},

                # 质量检查统计
                'spelling_errors': 0,
                'grammar_errors': 0,
                'revision_count': 0,
                'version_count': 0,
                'track_changes_count': 0,
                'formula_count': 0,
                'equation_count': 0,
                'textbox_count': 0,
                'chart_count': 0,
                'drawing_count': 0,
            }
        
        # 创建内容分析结果
        class ContentStats:
            def __init__(self, stats_dict, document_structures=None, outline=None, structure_analysis=None):
                for key, value in stats_dict.items():
                    setattr(self, key, value)
                self.content_stats = stats_dict
                # 🔥 新增：包含文档结构数据
                self.document_structures = document_structures or []
                self.outline = outline or []
                self.structure_analysis = structure_analysis or {}

        content_analysis = ContentStats(content_stats, document_structures, outline, structure_analysis)

        logger.info(f"🔍 内容分析完成，统计信息: {content_stats}")
        logger.info(f"🔍 ContentStats对象创建: document_structures={len(content_analysis.document_structures)}个, outline={len(content_analysis.outline)}个")
        return content_analysis

    def _count_words(self, text: str) -> int:
        """计算文本中的字数"""
        if not text:
            return 0
        import re
        # 去除标点符号和空白字符
        text = re.sub(r'[^\w\s]', '', text)
        # 分别计算中文字符和英文单词
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
        english_words = re.findall(r'[a-zA-Z]+', text)
        return len(chinese_chars) + len(english_words)
    
    def _add_warning(self, warning: str):
        """添加警告"""
        self._warnings.append(warning)
    
    def analyze_document_sync(self, file_path: str) -> AnalysisResult:
        """
        同步分析文档
        
        Args:
            file_path: 文档文件路径
            
        Returns:
            AnalysisResult: 分析结果
        """
        # 创建新的事件循环来运行异步方法
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self.analyze_document(file_path))
        finally:
            loop.close()
    
    def get_supported_formats(self) -> list:
        """获取支持的文件格式"""
        return ['.doc', '.docx'] 