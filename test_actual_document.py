#!/usr/bin/env python3
"""
测试实际文档的参考文献统计
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.document_processor import DocumentProcessor
import asyncio

def test_actual_document():
    """测试实际文档的参考文献统计"""
    
    document_path = os.path.abspath("docs/test.docx")
    
    if not os.path.exists(document_path):
        print(f"❌ 文档不存在: {document_path}")
        return
    
    print(f"🔍 测试实际文档: {document_path}")
    print("=" * 50)
    
    processor = DocumentProcessor()
    
    try:
        # 处理文档
        result = processor.analyze_document_comprehensive(document_path)
        
        if result and 'document_structures' in result:
            structures = result['document_structures']
            
            print(f"📊 检测到 {len(structures)} 个文档结构")
            print("-" * 30)
            
            # 查找参考文献结构
            reference_structure = None
            for structure in structures:
                structure_name = structure.get('name') or structure.get('structure_name', '')
                
                print(f"结构: {structure_name}")
                print(f"  - 字数: {structure.get('word_count', 0)}")
                print(f"  - 参考文献条数: {structure.get('reference_count', 0)}")
                
                if '参考文献' in structure_name:
                    reference_structure = structure
                    
                    # 检查是否有详细的中文/外文统计
                    if 'reference_chinese_count' in structure:
                        print(f"  - 中文参考文献: {structure.get('reference_chinese_count', 0)}条")
                        print(f"  - 外文参考文献: {structure.get('reference_foreign_count', 0)}条")
                    
                    if 'reference_display' in structure:
                        print(f"  - 显示文本: {structure.get('reference_display', '')}")
                
                print()
            
            if reference_structure:
                print("✅ 参考文献结构分析成功！")
                total_refs = reference_structure.get('reference_count', 0)
                chinese_refs = reference_structure.get('reference_chinese_count', 0)
                foreign_refs = reference_structure.get('reference_foreign_count', 0)
                display_text = reference_structure.get('reference_display', '')
                
                print(f"总条数: {total_refs}")
                print(f"中文: {chinese_refs}条")
                print(f"外文: {foreign_refs}条")
                print(f"显示: {display_text}")
                
                # 验证结果
                if total_refs == 14:
                    print("✅ 总条数正确（14条）")
                else:
                    print(f"❌ 总条数不正确，期望14条，实际{total_refs}条")
                
                if chinese_refs == 11 and foreign_refs == 3:
                    print("✅ 中文/外文分类正确")
                else:
                    print(f"❌ 中文/外文分类不正确，期望中文11条外文3条，实际中文{chinese_refs}条外文{foreign_refs}条")
                
                if display_text == "中文11条;外文3条":
                    print("✅ 显示文本格式正确")
                else:
                    print(f"❌ 显示文本格式不正确，期望'中文11条;外文3条'，实际'{display_text}'")
            else:
                print("❌ 未找到参考文献结构")
        else:
            print("❌ 文档处理失败或未返回结构数据")
            
    except Exception as e:
        print(f"❌ 处理文档时出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_actual_document()
