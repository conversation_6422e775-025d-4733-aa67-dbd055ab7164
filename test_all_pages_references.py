#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.document_processor import DocumentProcessor
from app.core.resource_manager import WordInstancePool

def test_all_pages_references():
    """测试所有页面的参考文献内容"""
    print("🔍 测试所有页面的参考文献内容")
    print("=" * 50)
    
    # 初始化资源管理器
    resource_manager = WordInstancePool()
    
    try:
        # 使用上下文管理器获取Word实例
        with resource_manager.get_instance() as word_instance:
            # 打开文档
            doc_path = r"D:\Works\paper-check-win\docs\test.docx"
            doc = word_instance.open_document(doc_path)
            
            # 创建文档处理器
            processor = DocumentProcessor(resource_manager)
            
            # 提取页面内容
            pages_content = processor._extract_pages_content(doc)
            
            print(f"文档总页数: {len(pages_content)}")
            
            # 检查每个页面的参考文献内容
            for page_num in sorted(pages_content.keys()):
                print(f"\n第{page_num}页的参考文献:")
                print("-" * 30)
                
                page_data = pages_content[page_num]
                all_paragraphs = []
                if isinstance(page_data, dict):
                    all_paragraphs.extend(page_data.get('paragraphs', []))
                    all_paragraphs.extend(page_data.get('tables', []))
                elif isinstance(page_data, list):
                    all_paragraphs = page_data
                
                ref_count = 0
                for paragraph in all_paragraphs:
                    if isinstance(paragraph, dict):
                        text = paragraph.get('text', '').strip()
                    else:
                        text = str(paragraph).strip()
                    
                    if not text:
                        continue
                    
                    # 清理文本
                    import re
                    cleaned_text = re.sub(r'[\r\x07\x0c]+', ' ', text)
                    cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
                    
                    # 检查是否是参考文献条目
                    if re.search(r'\[\d+\][^[\]]*\[[A-Za-z]\]', cleaned_text) or re.search(r'^\[\d+\][^[\]]*[.。][^[\]]*[,，][^[\]]*[.。]', cleaned_text):
                        ref_count += 1
                        # 提取编号
                        number_match = re.search(r'\[(\d+)\]', cleaned_text)
                        ref_number = number_match.group(1) if number_match else "?"
                        
                        # 判断语言
                        is_chinese = processor._is_chinese_reference(cleaned_text)
                        language = "中文" if is_chinese else "外文"
                        
                        print(f"  [{ref_number}] {language}: {cleaned_text[:80]}...")
                        
                        # 特别关注第4条参考文献
                        if ref_number == "4":
                            print(f"    *** 第4条参考文献详细内容: {cleaned_text}")
                
                if ref_count == 0:
                    print("  无参考文献")
                else:
                    print(f"  共{ref_count}条参考文献")
            
            # 关闭文档
            word_instance.close_document(doc)
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        resource_manager.shutdown()

if __name__ == "__main__":
    test_all_pages_references()
