"""
创建文档结构统计表的独立脚本
"""

import asyncio
from sqlalchemy import text
from app.core.logging import logger
from app.database.connection import get_database_session


async def create_structure_tables():
    """创建文档结构统计相关表"""
    
    # 创建文档结构统计表
    create_document_structures_sql = """
    CREATE TABLE IF NOT EXISTS document_structures (
        structure_id VARCHAR(50) PRIMARY KEY,
        document_id VARCHAR(50) NOT NULL,
        structure_name VARCHAR(200) NOT NULL,
        structure_type VARCHAR(20) NOT NULL DEFAULT 'standard',
        status VARCHAR(20) NOT NULL DEFAULT 'present',
        word_count INTEGER DEFAULT 0,
        reference_count INTEGER DEFAULT 0,
        page_number INTEGER,
        paragraph_index INTEGER,
        style_info JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    )
    """
    
    # 创建文档内容缓存表
    create_content_cache_sql = """
    CREATE TABLE IF NOT EXISTS document_content_cache (
        cache_id VARCHAR(50) PRIMARY KEY,
        document_id VARCHAR(50) NOT NULL,
        content_type VARCHAR(50) NOT NULL DEFAULT 'full_content',
        file_path VARCHAR(500) NOT NULL,
        file_size BIGINT DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT unique_document_content UNIQUE (document_id, content_type)
    )
    """
    
    # 创建索引的SQL语句
    create_indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_document_structures_document_id ON document_structures (document_id)",
        "CREATE INDEX IF NOT EXISTS idx_document_structures_type ON document_structures (structure_type)",
        "CREATE INDEX IF NOT EXISTS idx_document_structures_status ON document_structures (status)",
        "CREATE INDEX IF NOT EXISTS idx_content_cache_document_id ON document_content_cache (document_id)",
        "CREATE INDEX IF NOT EXISTS idx_content_cache_type ON document_content_cache (content_type)"
    ]
    
    session = await get_database_session()
    
    try:
        logger.info("开始创建文档结构统计表...")
        
        # 创建表
        await session.execute(text(create_document_structures_sql))
        logger.info("文档结构统计表创建成功")
        
        await session.execute(text(create_content_cache_sql))
        logger.info("文档内容缓存表创建成功")
        
        # 创建索引
        for index_sql in create_indexes_sql:
            try:
                await session.execute(text(index_sql))
                logger.info(f"索引创建成功")
            except Exception as e:
                logger.warning(f"索引创建失败: {e}")
        
        await session.commit()
        logger.info("所有表和索引创建完成!")
        
    except Exception as e:
        await session.rollback()
        logger.error(f"创建表失败: {e}")
        raise
    finally:
        await session.close()


if __name__ == "__main__":
    asyncio.run(create_structure_tables())
