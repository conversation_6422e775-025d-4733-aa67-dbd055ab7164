"""
测试学校和院系字段清理效果
"""

import re


def test_clean_department_value():
    """测试院系字段清理函数"""
    
    def _clean_department_value(value: str) -> str:
        """
        清理院系信息中的重复前缀和多余空格
        """
        if not value:
            return ''
        
        # 移除常见的重复前缀（只在开头匹配）
        prefixes_to_remove = [
            r'^院\s*系\s+',  # 移除开头的"院 系 "（后面必须有空格）
            r'^系\s*别\s+',  # 移除开头的"系 别 "（后面必须有空格）
        ]
        
        cleaned_value = value
        for prefix_pattern in prefixes_to_remove:
            cleaned_value = re.sub(prefix_pattern, '', cleaned_value)
        
        # 清理多余的空格
        cleaned_value = re.sub(r'\s+', ' ', cleaned_value).strip()

        # 🔥 额外处理：如果还有前缀残留，再次清理
        if cleaned_value.startswith('院 系') or cleaned_value.startswith('系 别'):
            # 找到第一个非前缀的内容
            parts = cleaned_value.split()
            if len(parts) >= 3:  # 至少有"院 系 学院名"三部分
                cleaned_value = ' '.join(parts[2:])  # 取第三部分开始的内容

        return cleaned_value
    
    print("🧪 测试院系字段清理...")
    
    test_cases = [
        ("院 系 艺术学院", "艺术学院"),
        ("院系艺术学院", "艺术学院"),
        ("学 院 计算机学院", "计算机学院"),
        ("学院计算机学院", "计算机学院"),
        ("系 别 中文系", "中文系"),
        ("系别中文系", "中文系"),
        ("艺术学院", "艺术学院"),  # 没有前缀的情况
        ("  院 系  艺术学院  ", "艺术学院"),  # 有多余空格
        ("", ""),  # 空字符串
    ]
    
    print("📋 院系字段清理测试结果:")
    for i, (input_value, expected) in enumerate(test_cases, 1):
        result = _clean_department_value(input_value)
        status = "✅" if result == expected else "❌"
        print(f"   {i}. {status} '{input_value}' -> '{result}' (期望: '{expected}')")
        if result != expected:
            print(f"      ❌ 测试失败: 期望 '{expected}', 实际 '{result}'")
    
    return all(_clean_department_value(inp) == exp for inp, exp in test_cases)


def test_clean_school_value():
    """测试学校字段清理函数"""
    
    def _clean_school_value(value: str) -> str:
        """
        清理学校信息中的重复前缀和多余空格
        """
        if not value:
            return ''
        
        # 移除常见的重复前缀（只在开头匹配）
        prefixes_to_remove = [
            r'^院\s*系\s+',  # 移除开头的"院 系 "（后面必须有空格）
            r'^学\s*校\s+',  # 移除开头的"学 校 "（后面必须有空格）
        ]
        
        cleaned_value = value
        for prefix_pattern in prefixes_to_remove:
            cleaned_value = re.sub(prefix_pattern, '', cleaned_value)
        
        # 清理多余的空格
        cleaned_value = re.sub(r'\s+', ' ', cleaned_value).strip()

        # 🔥 额外处理：如果还有前缀残留，再次清理
        if cleaned_value.startswith('院 系') or cleaned_value.startswith('学 校'):
            # 找到第一个非前缀的内容
            parts = cleaned_value.split()
            if len(parts) >= 3:  # 至少有"院 系 学院名"三部分
                cleaned_value = ' '.join(parts[2:])  # 取第三部分开始的内容

        return cleaned_value
    
    print("\n🧪 测试学校字段清理...")
    
    test_cases = [
        ("院 系 艺术学院", "艺术学院"),
        ("院系艺术学院", "艺术学院"),
        ("学 校 河北科技学院", "河北科技学院"),
        ("学校河北科技学院", "河北科技学院"),
        ("大 学 清华大学", "清华大学"),
        ("大学清华大学", "清华大学"),
        ("河北科技学院", "河北科技学院"),  # 没有前缀的情况
        ("  院 系  艺术学院  ", "艺术学院"),  # 有多余空格
        ("", ""),  # 空字符串
    ]
    
    print("📋 学校字段清理测试结果:")
    for i, (input_value, expected) in enumerate(test_cases, 1):
        result = _clean_school_value(input_value)
        status = "✅" if result == expected else "❌"
        print(f"   {i}. {status} '{input_value}' -> '{result}' (期望: '{expected}')")
        if result != expected:
            print(f"      ❌ 测试失败: 期望 '{expected}', 实际 '{result}'")
    
    return all(_clean_school_value(inp) == exp for inp, exp in test_cases)


def test_api_response_cleanup():
    """测试API响应清理效果"""
    
    print("\n🎯 模拟API响应清理效果...")
    
    # 模拟修复前的API响应
    before_cleanup = {
        "document_info": {
            "title": "新媒体技术对舞蹈编导创作手法的影响研究",
            "author": "李岩",
            "major": "舞蹈编导（专升本）",
            "department": "院 系 艺术学院",  # 🔥 有问题的字段
            "student_id": "32219350130",
            "advisor": "展烨",
            "date": "2025年5月20日",
            "degree_type": "学士学位论文",
            "school": "院 系 艺术学院"  # 🔥 有问题的字段
        }
    }
    
    # 模拟修复后的API响应
    after_cleanup = {
        "document_info": {
            "title": "新媒体技术对舞蹈编导创作手法的影响研究",
            "author": "李岩",
            "major": "舞蹈编导（专升本）",
            "department": "艺术学院",  # ✅ 清理后的字段
            "student_id": "32219350130",
            "advisor": "展烨",
            "date": "2025年5月20日",
            "degree_type": "学士学位论文",
            "school": "艺术学院"  # ✅ 清理后的字段
        }
    }
    
    print("📊 修复前后对比:")
    print("修复前:")
    print(f"   department: '{before_cleanup['document_info']['department']}'")
    print(f"   school: '{before_cleanup['document_info']['school']}'")
    
    print("\n修复后:")
    print(f"   department: '{after_cleanup['document_info']['department']}'")
    print(f"   school: '{after_cleanup['document_info']['school']}'")
    
    # 验证清理效果
    department_cleaned = "院 系" not in after_cleanup['document_info']['department']
    school_cleaned = "院 系" not in after_cleanup['document_info']['school']
    
    print(f"\n✅ 验证结果:")
    print(f"   department字段清理: {'✅' if department_cleaned else '❌'}")
    print(f"   school字段清理: {'✅' if school_cleaned else '❌'}")
    
    return department_cleaned and school_cleaned


def test_frontend_display_effect():
    """测试前端显示效果"""
    
    print("\n🎨 模拟前端显示效果...")
    
    # 修复后的数据
    clean_data = {
        "title": "新媒体技术对舞蹈编导创作手法的影响研究",
        "author": "李岩",
        "major": "舞蹈编导（专升本）",
        "department": "艺术学院",  # ✅ 清理后
        "student_id": "32219350130",
        "advisor": "展烨",
        "date": "2025年5月20日",
        "school": "艺术学院"  # ✅ 清理后
    }
    
    print("=" * 50)
    print("📄 文档信息 (修复后)")
    print("=" * 50)
    print(f"📝 论文题目")
    print(f"   {clean_data['title']}")
    print()
    print(f"👤 作者: {clean_data['author']}")
    print(f"🎓 学号: {clean_data['student_id']}")
    print(f"🏫 院系: {clean_data['department']}")  # ✅ 清理后显示
    print()
    print(f"📚 专业: {clean_data['major']}")
    print(f"👨‍🏫 指导老师: {clean_data['advisor']}")
    print(f"📅 论文时间: {clean_data['date']}")
    print(f"🏛️ 学校: {clean_data['school']}")  # ✅ 清理后显示
    print("=" * 50)
    
    # 验证没有多余空格
    has_extra_spaces = any("  " in str(value) for value in clean_data.values())
    has_prefix_issues = any("院 系" in str(value) for value in clean_data.values())
    
    print(f"\n✅ 显示质量验证:")
    print(f"   无多余空格: {'✅' if not has_extra_spaces else '❌'}")
    print(f"   无前缀问题: {'✅' if not has_prefix_issues else '❌'}")
    
    return not has_extra_spaces and not has_prefix_issues


def main():
    """主测试函数"""
    
    print("🧪 学校和院系字段清理测试")
    print("=" * 60)
    
    # 运行所有测试
    test_results = []
    
    test_results.append(test_clean_department_value())
    test_results.append(test_clean_school_value())
    test_results.append(test_api_response_cleanup())
    test_results.append(test_frontend_display_effect())
    
    # 总结测试结果
    print(f"\n📊 测试总结:")
    print(f"   通过的测试: {sum(test_results)}/{len(test_results)}")
    
    if all(test_results):
        print("✅ 所有测试通过！")
        print("🎉 学校和院系字段清理修复成功！")
        
        print(f"\n📋 修复效果:")
        print(f"   ✅ 移除了'院 系'等重复前缀")
        print(f"   ✅ 清理了多余的空格")
        print(f"   ✅ 保持了原有的有效信息")
        print(f"   ✅ 提升了前端显示质量")
    else:
        print("❌ 部分测试失败，需要进一步修复")
    
    return all(test_results)


if __name__ == "__main__":
    main()
