{"success": true, "code": 200, "message": "获取任务信息成功", "data": {"filename": "test.docx", "file_size": 2560385, "analysis_options": {"detection_standard": "hbkj_bachelor_2024"}, "user_id": "user_2450b8b44a9c4db0842e36cd9e99ed65", "task_id": "task_47efcaf29baa42a9a0c30fe62660a969", "file_path": "D:\\Works\\paper-check-win\\backend\\data\\uploads\\user_2450b8b44a9c4db0842e36cd9e99ed65\\task_47efcaf29baa42a9a0c30fe62660a969_test.docx", "task_type": "paper_check", "status": "completed", "progress": 100, "created_at": "2025-07-27T02:47:36.654357+00:00", "started_at": null, "completed_at": "2025-07-27T02:48:58.527141+00:00", "processing_time": null, "error_message": null, "result": {"success": true, "processing_time": 81.8379864692688, "error_message": null, "warnings": [], "document_info": {"title": "新媒体技术对舞蹈编导创作手法的影响研究", "author": "李岩", "major": "舞蹈编导（专升本）", "department": "艺术学院", "student_id": "32219350130", "advisor": "展烨", "date": "2025年5月20日", "degree_type": "学士学位论文", "school": "院 系 艺术学院"}, "content_stats": {"page_count": 36, "word_count": 18806, "image_count": 9, "table_count": 10, "formula_count": 0, "footnote_count": 0, "character_count": 22630, "paragraph_count": 514, "reference_count": 0}, "document_structures": [{"name": "封面", "page": 1, "type": "standard", "status": "present", "content": {"text": "学士学位论文", "style": "正文", "paragraph_index": 1}, "required": true, "word_count": 6, "reference_count": 0}, {"name": "任务书", "page": 2, "type": "standard", "status": "present", "content": {"text": "河北科技学院本科生毕业设计（论文）任务书", "style": "任务书-标题", "paragraph_index": 0}, "required": true, "word_count": 18, "reference_count": 0}, {"name": "开题报告", "page": 3, "type": "standard", "status": "present", "content": {"text": "河北科技学院本科生毕业设计(论文)开题报告", "style": "开题报告-标题", "paragraph_index": 0}, "required": true, "word_count": 19, "reference_count": 0}, {"name": "河北科技学院本科生毕业设计（论文）测试", "page": 7, "type": "non_standard", "status": "present", "content": {"text": "河北科技学院本科生毕业设计（论文）测试", "style": "任务书-标题", "paragraph_index": 0}, "required": false, "word_count": 17, "reference_count": 0}, {"name": "诚信声明", "page": 9, "type": "standard", "status": "present", "content": {"text": "学位论文原创性声明", "style": "标题 1- 非目录", "paragraph_index": 0}, "required": true, "word_count": 9, "reference_count": 0}, {"name": "版权声明", "page": 9, "type": "standard", "status": "present", "content": {"text": "学位论文版权使用授权书", "style": "标题 1- 非目录", "paragraph_index": 5}, "required": true, "word_count": 11, "reference_count": 0}, {"name": "河北科技学院本科生毕业设计（论文）测试2", "page": 10, "type": "non_standard", "status": "present", "content": {"text": "河北科技学院本科生毕业设计（论文）测试2", "style": "任务书-标题", "paragraph_index": 0}, "required": false, "word_count": 17, "reference_count": 0}, {"name": "中文摘要", "page": 11, "type": "standard", "status": "present", "content": {"text": "摘　　要", "style": "标题 1- 非目录", "paragraph_index": 0}, "required": true, "word_count": 2, "reference_count": 0}, {"name": "中文关键词", "page": 11, "type": "standard", "status": "present", "content": {"text": "关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）；短视频传播；编导创作手法", "style": "正文", "paragraph_index": 2}, "required": true, "word_count": 0, "reference_count": 0}, {"name": "英文摘要", "page": 12, "type": "standard", "status": "present", "content": {"text": "ABSTRACT", "style": "标题 1- 非目录", "paragraph_index": 0}, "required": true, "word_count": 1, "reference_count": 0}, {"name": "英文关键词", "page": 12, "type": "standard", "status": "present", "content": {"text": "Key words:dance creation;virtual reality (VR); augmented reality (AR); short video communication; ch", "style": "正文", "paragraph_index": 2}, "required": true, "word_count": 0, "reference_count": 0}, {"name": "目录", "page": 13, "type": "standard", "status": "present", "content": {"text": "目　　录", "style": "标题 1- 非目录", "paragraph_index": 0}, "required": true, "word_count": 0, "reference_count": 0}, {"name": "正文", "page": 15, "type": "standard", "status": "present", "content": {"text": "绪论", "style": "标题 1", "paragraph_index": 0}, "required": true, "word_count": 2, "reference_count": 0}, {"name": "结　　论", "page": 32, "type": "non_standard", "status": "present", "content": {"text": "结　　论", "style": "标题1 - 目录 - 非编号", "paragraph_index": 0}, "required": false, "word_count": 2, "reference_count": 0}, {"name": "参考文献", "page": 33, "type": "standard", "status": "present", "content": {"text": "参考文献", "style": "标题1 - 目录 - 非编号", "paragraph_index": 0}, "required": true, "word_count": 4, "reference_count": 1}, {"name": "列表啊", "page": 34, "type": "non_standard", "status": "present", "content": {"text": "列表啊", "style": "标题1 - 目录 - 非编号", "paragraph_index": 0}, "required": false, "word_count": 3, "reference_count": 0}, {"name": "致谢", "page": 35, "type": "standard", "status": "present", "content": {"text": "致　　谢", "style": "标题1 - 目录 - 非编号", "paragraph_index": 0}, "required": true, "word_count": 2, "reference_count": 0}, {"name": "列表啊2", "page": 36, "type": "non_standard", "status": "present", "content": {"text": "列表啊2", "style": "标题1 - 目录 - 非编号", "paragraph_index": 0}, "required": false, "word_count": 3, "reference_count": 0}, {"name": "附录", "page": null, "type": "standard", "status": "missing", "required": true, "word_count": 0, "reference_count": 0}], "outline": [{"page": 1, "text": "学士学位论文", "type": "standard", "level": 0, "structure_name": "封面"}, {"page": 2, "text": "河北科技学院本科生毕业设计（论文）任务书", "type": "standard", "level": 1, "structure_name": "任务书"}, {"page": 3, "text": "河北科技学院本科生毕业设计(论文)开题报告", "type": "standard", "level": 1, "structure_name": "开题报告"}, {"page": 7, "text": "河北科技学院本科生毕业设计（论文）测试", "type": "non_standard", "level": 2, "structure_name": "河北科技学院本科生毕业设计（论文）测试"}, {"page": 9, "text": "学位论文原创性声明", "type": "standard", "level": 1, "structure_name": "诚信声明"}, {"page": 9, "text": "学位论文版权使用授权书", "type": "standard", "level": 1, "structure_name": "版权声明"}, {"page": 10, "text": "河北科技学院本科生毕业设计（论文）测试2", "type": "non_standard", "level": 2, "structure_name": "河北科技学院本科生毕业设计（论文）测试2"}, {"page": 11, "text": "摘　　要", "type": "standard", "level": 1, "structure_name": "中文摘要"}, {"page": 11, "text": "关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）；短视频传播；编导创作手法", "type": "standard", "level": 2, "structure_name": "中文关键词"}, {"page": 12, "text": "ABSTRACT", "type": "standard", "level": 1, "structure_name": "英文摘要"}, {"page": 12, "text": "Key words:dance creation;virtual reality (VR); aug", "type": "standard", "level": 2, "structure_name": "英文关键词"}, {"page": 13, "text": "目　　录", "type": "standard", "level": 1, "structure_name": "目录"}, {"page": 15, "text": "绪论", "type": "standard", "level": 1, "structure_name": "正文"}, {"page": 32, "text": "结　　论", "type": "non_standard", "level": 2, "structure_name": "结　　论"}, {"page": 33, "text": "参考文献", "type": "standard", "level": 1, "structure_name": "参考文献"}, {"page": 34, "text": "列表啊", "type": "non_standard", "level": 2, "structure_name": "列表啊"}, {"page": 35, "text": "致　　谢", "type": "standard", "level": 1, "structure_name": "致谢"}, {"page": 36, "text": "列表啊2", "type": "non_standard", "level": 2, "structure_name": "列表啊2"}], "detection_standard": "hbkj_bachelor_2024", "standard_name": "河北科技学院学士论文检测标准 (2024版)", "task_type": "paper_check", "status": "completed", "compliance_score": 85, "problems_found": 0, "check_summary": {"major_problems": 0, "minor_problems": 0, "total_problems": 0, "compliance_score": 85}, "analysis_summary": {"document_type": "report", "quality_score": 0, "hierarchy_score": 0, "completeness_score": 0}, "processing_meta": {"processing_times": {"preprocessing": 0.006683349609375, "raw_extraction": 78.62934184074402, "structure_analysis": 0.002816915512084961}, "extraction_method": "word_com_pool", "processing_pipeline": "optimized_v2"}}, "updated_at": "2025-07-27T02:48:58.531445+00:00", "current_step": "step_4", "steps": {"step_1": {"step_id": "step_1", "name": "文档验证", "description": "文档验证完成", "progress": 100, "status": "completed", "start_time": "2025-07-27T02:47:36.667358", "end_time": "2025-07-27T02:47:37.176335", "error_message": null}, "step_2": {"step_id": "step_2", "name": "原始数据提取", "description": "数据提取成功", "progress": 100, "status": "completed", "start_time": "2025-07-27T02:47:37.176335", "end_time": "2025-07-27T02:48:58.155156", "error_message": null}, "step_3": {"step_id": "step_3", "name": "数据预处理", "description": "数据预处理成功", "progress": 100, "status": "completed", "start_time": "2025-07-27T02:48:58.155156", "end_time": "2025-07-27T02:48:58.163879", "error_message": null}, "step_4": {"step_id": "step_4", "name": "结构分析", "description": "分析报告已生成", "progress": 100, "status": "completed", "start_time": "2025-07-27T02:48:58.163879", "end_time": "2025-07-27T02:48:58.505344", "error_message": null}, "step_5": {"step_id": "step_5", "name": "格式规则检测", "description": "格式检测完成", "progress": 100, "status": "completed", "start_time": "2025-07-27T02:48:58.168701", "end_time": "2025-07-27T02:48:58.195830", "error_message": null}, "step_6": {"step_id": "step_6", "name": "生成分析报告", "description": "正在生成分析报告", "progress": 100, "status": "completed", "start_time": "2025-07-27T02:48:58.195830", "end_time": "2025-07-27T02:48:58.524140", "error_message": null}}, "estimated_completion": "2025-07-27T02:48:58.505344", "live_progress": true}, "timestamp": 1753584569, "request_id": null}