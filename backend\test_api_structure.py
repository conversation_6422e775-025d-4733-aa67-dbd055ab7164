"""
测试优化后的API结构
"""

import requests
import json


def test_api_structure():
    """测试API结构"""

    # 测试结构统计API（不需要认证）
    task_id = 'task_d3d714e7dba04c87b1bebb753a21b133'
    url = f'http://localhost:8000/api/v1/tasks/{task_id}/structure-stats'

    try:
        response = requests.get(url)
        if response.status_code == 200:
            data = response.json()

            print('📊 结构统计API测试:')
            print(f'✅ success: {data.get("success")}')
            print(f'✅ message: {data.get("message")}')

            # 检查数据结构
            api_data = data.get('data', {})
            print(f'✅ document_id: {api_data.get("document_id")}')
            print(f'✅ total_structures: {api_data.get("total_structures")}')
            print(f'✅ standard_structures: {api_data.get("standard_structures")}')
            print(f'✅ non_standard_structures: {api_data.get("non_standard_structures")}')

            # 检查结构数据
            structures = api_data.get('structures', [])
            print(f'📋 结构数量: {len(structures)}')

            if structures:
                non_standard_count = len([s for s in structures if s.get('structure_type') == 'non_standard'])
                print(f'🔍 非标准结构数量: {non_standard_count}')

                # 显示前几个结构
                print('\n📋 前3个结构:')
                for i, structure in enumerate(structures[:3]):
                    print(f'  {i+1}. {structure.get("structure_name")} - {structure.get("structure_type")} - {structure.get("word_count", 0)}字')
            else:
                print('⚠️ 没有找到结构数据，可能需要重新上传文档')
                
        else:
            print(f'❌ API请求失败: {response.status_code}')
            
    except Exception as e:
        print(f'❌ 请求异常: {e}')


if __name__ == "__main__":
    test_api_structure()
