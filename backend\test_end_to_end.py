"""
端到端测试：验证35个统计字段
"""

import requests
import json
import time
import os


def test_upload_and_check():
    """上传文档并检查统计字段"""
    
    print("🧪 端到端测试：验证35个统计字段")
    print("=" * 60)
    
    # 测试配置
    base_url = "http://localhost:8000"
    username = "8966097"
    password = "heibalan5112"
    test_file = "docs/test.docx"
    
    # 检查测试文件是否存在
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return False
    
    print(f"✅ 测试文件存在: {test_file}")
    
    session = requests.Session()
    
    try:
        # 1. 登录
        print("\n🔐 步骤1: 用户登录")
        login_data = {
            "username": username,
            "password": password
        }
        
        login_response = session.post(f"{base_url}/api/v1/auth/login", json=login_data)
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            if login_result.get("success"):
                token = login_result["data"]["access_token"]
                session.headers.update({"Authorization": f"Bearer {token}"})
                print("✅ 登录成功")
            else:
                print(f"❌ 登录失败: {login_result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 登录请求失败: {login_response.status_code}")
            return False
        
        # 2. 上传文档
        print("\n📤 步骤2: 上传测试文档")
        
        with open(test_file, 'rb') as f:
            files = {
                'file': (os.path.basename(test_file), f, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
            }
            data = {
                'task_type': 'paper_check',
                'analysis_options': json.dumps({
                    "detection_standard": "hbkj_bachelor_2024",
                    "check_format": True,
                    "check_structure": True,
                    "check_citation": True,
                    "check_reference": True
                })
            }
            
            upload_response = session.post(f"{base_url}/api/v1/documents/upload", files=files, data=data)
        
        if upload_response.status_code == 200:
            upload_result = upload_response.json()
            if upload_result.get("success"):
                task_id = upload_result["data"]["task_id"]
                print(f"✅ 文档上传成功，任务ID: {task_id}")
            else:
                print(f"❌ 文档上传失败: {upload_result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 上传请求失败: {upload_response.status_code}")
            print(f"响应内容: {upload_response.text}")
            return False
        
        # 3. 等待任务完成
        print("\n⏳ 步骤3: 等待任务完成")
        
        max_wait_time = 600  # 最多等待10分钟
        check_interval = 10  # 每10秒检查一次
        
        for i in range(max_wait_time // check_interval):
            time.sleep(check_interval)
            
            # 检查任务状态
            status_response = session.get(f"{base_url}/api/v1/tasks/{task_id}")
            
            if status_response.status_code == 200:
                status_result = status_response.json()
                if status_result.get("success"):
                    task_data = status_result["data"]
                    status = task_data.get("status")
                    progress = task_data.get("progress", 0)
                    
                    print(f"   任务状态: {status}, 进度: {progress}%")
                    
                    if status == "completed":
                        print("✅ 任务完成")
                        break
                    elif status == "failed":
                        print(f"❌ 任务失败: {task_data.get('error_message', '未知错误')}")
                        return False
                else:
                    print(f"❌ 获取任务状态失败: {status_result.get('message', '未知错误')}")
                    return False
            else:
                print(f"❌ 状态查询请求失败: {status_response.status_code}")
                return False
        else:
            print("❌ 任务等待超时")
            return False
        
        # 4. 检查统计字段
        print("\n📊 步骤4: 检查统计字段")
        
        # 再次获取任务结果
        result_response = session.get(f"{base_url}/api/v1/tasks/{task_id}")
        
        if result_response.status_code == 200:
            result_data = result_response.json()
            if result_data.get("success"):
                task_result = result_data["data"]["result"]
                
                if "content_stats" in task_result:
                    content_stats = task_result["content_stats"]
                    
                    print(f"📋 content_stats字段数: {len(content_stats)}")
                    print(f"📋 content_stats字段列表:")
                    
                    for i, (field, value) in enumerate(content_stats.items(), 1):
                        print(f"   {i:2d}. {field}: {value}")
                    
                    # 检查期望的35个字段
                    expected_fields = [
                        # 学术论文必需统计
                        'page_count', 'word_count', 'character_count', 'characters_with_spaces',
                        'paragraph_count', 'table_count', 'image_count', 'line_count',
                        
                        # 结构分析统计
                        'heading_count', 'section_count', 'footnote_count', 'endnote_count',
                        'reference_count', 'hyperlink_count', 'bookmark_count', 'comment_count', 'field_count',
                        
                        # 格式规范统计
                        'font_count', 'style_count', 'fonts_used', 'styles_used',
                        'page_orientation', 'page_size', 'margin_info', 'line_spacing_info',
                        
                        # 质量检查统计
                        'spelling_errors', 'grammar_errors', 'revision_count', 'version_count',
                        'track_changes_count', 'formula_count', 'equation_count', 'textbox_count',
                        'chart_count', 'drawing_count'
                    ]
                    
                    missing_fields = [field for field in expected_fields if field not in content_stats]
                    
                    print(f"\n📊 完整性检查:")
                    print(f"   期望字段数: {len(expected_fields)}")
                    print(f"   实际字段数: {len(content_stats)}")
                    print(f"   缺少字段数: {len(missing_fields)}")
                    
                    if missing_fields:
                        print(f"   缺少的字段: {missing_fields}")
                    
                    # 保存结果到文件
                    with open("backend/result.json", "w", encoding="utf-8") as f:
                        json.dump(result_data, f, ensure_ascii=False, indent=2)
                    print(f"   结果已保存到: backend/result.json")
                    
                    if len(content_stats) == len(expected_fields) and not missing_fields:
                        print("\n🎉 测试成功：API返回完整的35个统计字段！")
                        return True
                    else:
                        print("\n❌ 测试失败：API返回的统计字段不完整")
                        
                        # 分析缺失的字段类型
                        basic_missing = [f for f in missing_fields if f in expected_fields[:8]]
                        structure_missing = [f for f in missing_fields if f in expected_fields[8:17]]
                        format_missing = [f for f in missing_fields if f in expected_fields[17:25]]
                        quality_missing = [f for f in missing_fields if f in expected_fields[25:]]
                        
                        if basic_missing:
                            print(f"   缺少基础统计字段: {basic_missing}")
                        if structure_missing:
                            print(f"   缺少结构分析字段: {structure_missing}")
                        if format_missing:
                            print(f"   缺少格式规范字段: {format_missing}")
                        if quality_missing:
                            print(f"   缺少质量检查字段: {quality_missing}")
                        
                        return False
                        
                else:
                    print("❌ API响应中没有content_stats字段")
                    return False
            else:
                print(f"❌ 获取任务结果失败: {result_data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 结果查询请求失败: {result_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_upload_and_check()
    
    if success:
        print("\n🎉 端到端测试成功！")
    else:
        print("\n❌ 端到端测试失败！")
        print("\n🔧 可能的问题:")
        print("   1. Word COM接口没有正确读取35个字段")
        print("   2. 数据传递过程中丢失了字段")
        print("   3. 任务管理器的字段提取逻辑有问题")
        print("   4. 数据库存储时字段被过滤")
