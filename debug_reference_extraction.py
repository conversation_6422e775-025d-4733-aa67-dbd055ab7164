#!/usr/bin/env python3
"""
调试参考文献提取逻辑
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.document_processor import DocumentProcessor

def debug_reference_extraction():
    """调试参考文献提取"""
    
    document_path = os.path.abspath("docs/test.docx")
    
    if not os.path.exists(document_path):
        print(f"❌ 文档不存在: {document_path}")
        return
    
    print(f"🔍 调试参考文献提取: {document_path}")
    print("=" * 50)
    
    processor = DocumentProcessor()
    
    try:
        # 处理文档
        result = processor.analyze_document_comprehensive(document_path)
        
        if result and 'document_structures' in result:
            structures = result['document_structures']
            
            # 查找参考文献结构
            reference_structure = None
            for structure in structures:
                structure_name = structure.get('name') or structure.get('structure_name', '')
                
                if '参考文献' in structure_name:
                    reference_structure = structure
                    break
            
            if reference_structure:
                print("📊 参考文献结构详细信息:")
                print(f"  - 结构名称: {reference_structure.get('name', '')}")
                print(f"  - 页码: {reference_structure.get('page', '')}")
                print(f"  - 字数: {reference_structure.get('word_count', 0)}")
                print(f"  - 参考文献条数: {reference_structure.get('reference_count', 0)}")
                
                # 检查内容
                content = reference_structure.get('content', {})
                text = content.get('text', '')
                
                print(f"\n📝 参考文献文本内容 (前500字符):")
                print("-" * 30)
                print(text[:500])
                print("-" * 30)
                
                print(f"\n📝 参考文献完整文本:")
                print("-" * 30)
                print(text)
                print("-" * 30)
                
                # 手动测试参考文献统计
                print(f"\n🔍 手动测试参考文献统计:")
                chinese_count, foreign_count = processor._count_references_by_language(text)
                total_count = processor._count_references_in_text(text)
                
                print(f"  - 总条数: {total_count}")
                print(f"  - 中文: {chinese_count}条")
                print(f"  - 外文: {foreign_count}条")
                
                # 分析每一行
                print(f"\n📋 逐行分析:")
                lines = [line.strip() for line in text.split('\n') if line.strip()]
                
                import re
                reference_patterns = [
                    r'^\s*\[\d+\]',  # [1] 格式
                    r'^\s*\d+\.\s*',  # 1. 格式  
                    r'^\s*\(\d+\)',   # (1) 格式
                ]
                
                for i, line in enumerate(lines):
                    is_reference = False
                    for pattern in reference_patterns:
                        if re.match(pattern, line):
                            is_reference = True
                            break
                    
                    is_chinese = processor._is_chinese_reference(line)
                    language = "中文" if is_chinese else "外文"
                    
                    print(f"  第{i+1}行: {'✅' if is_reference else '❌'} {language} - {line[:50]}...")
                
            else:
                print("❌ 未找到参考文献结构")
        else:
            print("❌ 文档处理失败或未返回结构数据")
            
    except Exception as e:
        print(f"❌ 处理文档时出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_reference_extraction()
