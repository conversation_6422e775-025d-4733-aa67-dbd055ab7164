#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.document_processor import DocumentProcessor
from app.core.resource_manager import WordInstancePool
import re

def debug_page_data():
    """调试页面数据结构"""
    print("🔍 调试页面数据结构")
    print("=" * 50)

    # 初始化资源管理器
    resource_manager = WordInstancePool()

    # 创建文档处理器
    processor = DocumentProcessor(resource_manager)

    try:
        # 使用上下文管理器获取Word实例
        with resource_manager.get_instance() as word_instance:
            # 打开文档
            doc_path = r"D:\Works\paper-check-win\docs\test.docx"
            doc = word_instance.open_document(doc_path)

            # 提取页面内容
            pages_content = processor._extract_pages_content(doc)

            # 检查第2页的数据结构
            page_2_data = pages_content.get(2, [])

            print(f"第2页段落数量: {len(page_2_data)}")

            ref_count = 0
            for i, paragraph in enumerate(page_2_data):
                text = paragraph.get('text', '').strip()
                if not text:
                    continue

                # 检查是否包含参考文献
                if '[1]' in text or '[2]' in text or '[3]' in text or '[4]' in text:
                    print(f"\n段落 {i}: {repr(text[:100])}...")

                    # 清理文本
                    cleaned_text = re.sub(r'[\r\x07\x0c]+', ' ', text)
                    cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
                    print(f"清理后: {repr(cleaned_text[:100])}...")

                    # 测试正则表达式
                    pattern1 = r'\[\d+\][^[\]]*\[[A-Za-z]\]'
                    pattern2 = r'\[\d+\][^[\]]{10,}'

                    match1 = re.search(pattern1, cleaned_text)
                    match2 = re.search(pattern2, cleaned_text)

                    print(f"模式1匹配: {bool(match1)}")
                    print(f"模式2匹配: {bool(match2)}")

                    if match1 or match2:
                        ref_count += 1
                        print(f"✅ 参考文献条目 {ref_count}")
                    else:
                        print("❌ 不匹配")

            print(f"\n总计找到参考文献条目: {ref_count}")

            # 关闭文档
            word_instance.close_document()

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理资源
        resource_manager.shutdown()

if __name__ == "__main__":
    debug_page_data()
