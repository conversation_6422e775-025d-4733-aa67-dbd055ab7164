"""
触发重新分析以验证35个统计字段
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.database import get_db
from app.database import crud
from app.tasks.manager import task_manager
from app.models.task import TaskCreate, TaskStatus, TaskType
import uuid


async def trigger_reanalysis():
    """触发重新分析"""
    
    print("🔄 触发重新分析以验证35个统计字段...")
    
    # 原始任务ID
    original_task_id = "task_eda817c0c6cc455794f6fb066c86410b"
    
    try:
        # 获取数据库会话
        async for session in get_db():
            # 1. 获取原始任务信息
            original_task = await crud.get_task(session, original_task_id)
            if not original_task:
                print(f"❌ 原始任务未找到: {original_task_id}")
                return False
            
            print(f"✅ 找到原始任务: {original_task_id}")
            print(f"   文件路径: {original_task.file_path}")
            print(f"   任务类型: {original_task.task_type}")
            print(f"   状态: {original_task.status}")
            
            # 2. 创建新的重新分析任务
            new_task_id = f"reanalyze_{uuid.uuid4().hex}"
            
            # 创建任务数据
            task_data = TaskCreate(
                task_id=new_task_id,
                task_type=TaskType.PAPER_CHECK,  # 使用论文检测类型
                file_path=original_task.file_path,  # 使用相同的文件
                user_id=original_task.user_id,  # 使用相同的用户
                status=TaskStatus.PENDING,
                priority=1,
                analysis_options={
                    "detection_standard": "hbkj_bachelor_2024",
                    "check_format": True,
                    "check_structure": True,
                    "check_citation": True,
                    "check_reference": True
                },
                description=f"重新分析任务 - 验证35个统计字段 (原始任务: {original_task_id})"
            )
            
            # 3. 保存到数据库
            await crud.create_task(session, task_data)
            print(f"✅ 创建重新分析任务: {new_task_id}")
            
            # 4. 启动后台任务处理
            print("🚀 启动后台任务处理...")
            asyncio.create_task(task_manager.process_task(new_task_id))
            
            print(f"🎉 重新分析任务已启动！")
            print(f"   新任务ID: {new_task_id}")
            print(f"   原始任务ID: {original_task_id}")
            print(f"   预计完成时间: 5-10分钟")
            
            print(f"\n📋 验证步骤:")
            print(f"   1. 等待任务完成（约5-10分钟）")
            print(f"   2. 访问API: http://localhost:8000/api/v1/tasks/{new_task_id}")
            print(f"   3. 检查content_stats是否包含35个字段")
            
            return new_task_id
            
    except Exception as e:
        print(f"❌ 触发重新分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def check_task_status(task_id: str):
    """检查任务状态"""
    
    print(f"\n🔍 检查任务状态: {task_id}")
    
    try:
        async for session in get_db():
            task = await crud.get_task(session, task_id)
            if task:
                print(f"   状态: {task.status}")
                print(f"   进度: {task.progress}%")
                print(f"   创建时间: {task.created_at}")
                print(f"   更新时间: {task.updated_at}")
                
                if task.status == TaskStatus.COMPLETED:
                    print("✅ 任务已完成！")
                    return True
                elif task.status == TaskStatus.FAILED:
                    print("❌ 任务失败")
                    print(f"   错误信息: {task.error_message}")
                    return False
                else:
                    print("⏳ 任务进行中...")
                    return None
            else:
                print("❌ 任务未找到")
                return False
                
    except Exception as e:
        print(f"❌ 检查任务状态失败: {str(e)}")
        return False


async def main():
    """主函数"""
    
    print("🧪 重新分析验证35个统计字段")
    print("=" * 50)
    
    # 触发重新分析
    new_task_id = await trigger_reanalysis()
    
    if new_task_id:
        print(f"\n⏳ 等待任务完成...")
        
        # 每30秒检查一次任务状态
        for i in range(20):  # 最多等待10分钟
            await asyncio.sleep(30)
            
            status = await check_task_status(new_task_id)
            
            if status is True:
                print(f"\n🎉 任务完成！现在可以检查API响应:")
                print(f"   API地址: http://localhost:8000/api/v1/tasks/{new_task_id}")
                print(f"   期望结果: content_stats包含35个统计字段")
                break
            elif status is False:
                print(f"\n❌ 任务失败，请检查日志")
                break
            else:
                print(f"   等待中... ({i+1}/20)")
        
        else:
            print(f"\n⏰ 等待超时，请手动检查任务状态")
            print(f"   任务ID: {new_task_id}")
    
    else:
        print(f"\n❌ 重新分析启动失败")


if __name__ == "__main__":
    asyncio.run(main())
