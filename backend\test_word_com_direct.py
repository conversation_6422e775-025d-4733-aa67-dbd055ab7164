"""
直接测试Word COM接口读取35个统计字段
"""

import sys
import os
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app.services.word_com import get_word_application
    
    def test_word_com_statistics():
        """测试Word COM接口统计信息读取"""
        
        print("🧪 直接测试Word COM接口读取35个统计字段")
        print("=" * 60)
        
        # 测试文件路径
        test_file = "D:\\Works\\paper-check-win\\backend\\data\\uploads\\user_2450b8b44a9c4db0842e36cd9e99ed65\\task_cbec6ee040c7434daf0746f053b08286_test.docx"
        
        if not os.path.exists(test_file):
            print(f"❌ 测试文件不存在: {test_file}")
            return False
        
        print(f"✅ 测试文件存在: {test_file}")
        
        try:
            # 获取Word应用程序
            word_app = get_word_application()
            
            if not word_app.start():
                print("❌ Word应用程序启动失败")
                return False
            
            print("✅ Word应用程序启动成功")
            
            # 打开文档
            doc = word_app.open_document(test_file, read_only=True)
            
            if not doc:
                print("❌ 文档打开失败")
                return False
            
            print("✅ 文档打开成功")
            
            # 获取文档信息（包含统计信息）
            doc_info = word_app.get_document_info(doc)
            
            print(f"\n📊 Word COM接口返回的统计信息:")
            print(f"   字段数量: {len(doc_info)}")
            
            # 提取统计相关字段
            stats_fields = {}
            for key, value in doc_info.items():
                if any(stat_key in key.lower() for stat_key in [
                    'page', 'word', 'character', 'paragraph', 'table', 'image', 'line',
                    'heading', 'section', 'footnote', 'endnote', 'reference', 'hyperlink', 
                    'bookmark', 'comment', 'field', 'font', 'style', 'orientation', 'size',
                    'margin', 'spacing', 'spelling', 'grammar', 'revision', 'version',
                    'track', 'formula', 'equation', 'textbox', 'chart', 'drawing'
                ]):
                    stats_fields[key] = value
            
            print(f"\n📋 统计相关字段 ({len(stats_fields)} 个):")
            for i, (key, value) in enumerate(stats_fields.items(), 1):
                print(f"   {i:2d}. {key}: {value}")
            
            # 检查是否包含我们期望的35个字段
            expected_fields = [
                # 学术论文必需统计
                'stat_pages', 'stat_words', 'stat_characters', 'characters_with_spaces',
                'stat_paragraphs', 'stat_tables', 'stat_images', 'lines',
                
                # 结构分析统计
                'heading_count', 'section_count', 'footnote_count', 'endnote_count',
                'reference_count', 'hyperlink_count', 'bookmark_count', 'comment_count', 'field_count',
                
                # 格式规范统计
                'font_count', 'style_count', 'fonts_used', 'styles_used',
                'page_orientation', 'page_size', 'margin_info', 'line_spacing_info',
                
                # 质量检查统计
                'spelling_errors', 'grammar_errors', 'revision_count', 'version_count',
                'track_changes_count', 'formula_count', 'equation_count', 'textbox_count',
                'chart_count', 'drawing_count'
            ]
            
            found_fields = []
            missing_fields = []
            
            for field in expected_fields:
                if field in doc_info:
                    found_fields.append(field)
                else:
                    missing_fields.append(field)
            
            print(f"\n📊 字段检查结果:")
            print(f"   期望字段数: {len(expected_fields)}")
            print(f"   找到字段数: {len(found_fields)}")
            print(f"   缺少字段数: {len(missing_fields)}")
            
            if found_fields:
                print(f"\n✅ 找到的字段:")
                for field in found_fields:
                    print(f"   - {field}: {doc_info[field]}")
            
            if missing_fields:
                print(f"\n❌ 缺少的字段:")
                for field in missing_fields:
                    print(f"   - {field}")
            
            # 保存完整的文档信息到文件
            with open("word_com_output.json", "w", encoding="utf-8") as f:
                json.dump(doc_info, f, ensure_ascii=False, indent=2, default=str)
            print(f"\n💾 完整的Word COM输出已保存到: word_com_output.json")
            
            # 关闭文档
            word_app.close_document(doc)
            print("✅ 文档已关闭")
            
            # 分析结果
            if len(found_fields) == len(expected_fields):
                print("\n🎉 Word COM接口成功读取了所有35个统计字段！")
                return True
            else:
                print(f"\n❌ Word COM接口只读取了 {len(found_fields)}/{len(expected_fields)} 个统计字段")
                
                # 分析缺失的字段类型
                basic_missing = [f for f in missing_fields if f in expected_fields[:8]]
                structure_missing = [f for f in missing_fields if f in expected_fields[8:17]]
                format_missing = [f for f in missing_fields if f in expected_fields[17:25]]
                quality_missing = [f for f in missing_fields if f in expected_fields[25:]]
                
                if basic_missing:
                    print(f"   缺少基础统计字段: {basic_missing}")
                if structure_missing:
                    print(f"   缺少结构分析字段: {structure_missing}")
                if format_missing:
                    print(f"   缺少格式规范字段: {format_missing}")
                if quality_missing:
                    print(f"   缺少质量检查字段: {quality_missing}")
                
                return False
                
        except Exception as e:
            print(f"❌ Word COM测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            try:
                word_app.stop()
                print("✅ Word应用程序已关闭")
            except:
                pass
    
    if __name__ == "__main__":
        success = test_word_com_statistics()
        
        if success:
            print("\n🎉 Word COM接口测试成功！")
        else:
            print("\n❌ Word COM接口测试失败！")
            print("\n🔧 可能的问题:")
            print("   1. Word COM接口的新方法没有被调用")
            print("   2. 新方法中有异常导致字段没有添加")
            print("   3. 字段名称不匹配")
            print("   4. Word文档本身不包含这些信息")

except ImportError as e:
    print(f"❌ 模块导入失败: {str(e)}")
    print("请确保在正确的环境中运行此脚本")
