#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.document_processor import DocumentProcessor
from app.core.resource_manager import WordInstancePool

def test_detailed_reference_analysis():
    """详细测试参考文献分析"""
    print("🔍 详细测试参考文献分析")
    print("=" * 50)
    
    # 初始化资源管理器
    resource_manager = WordInstancePool()
    
    try:
        # 使用上下文管理器获取Word实例
        with resource_manager.get_instance() as word_instance:
            # 打开文档
            doc_path = r"D:\Works\paper-check-win\docs\test.docx"
            doc = word_instance.open_document(doc_path)
            
            # 创建文档处理器
            processor = DocumentProcessor(resource_manager)
            
            # 提取页面内容
            pages_content = processor._extract_pages_content(doc)
            
            # 查找参考文献页面
            reference_page = processor._find_actual_reference_page(pages_content)

            # 提取参考文献内容
            ref_content = processor._extract_reference_content_across_pages(pages_content, reference_page)
            
            print(f"提取的参考文献内容长度: {len(ref_content)}")
            print("\n提取的参考文献内容:")
            print("-" * 30)
            print(ref_content)
            print("-" * 30)
            
            # 分析每一行
            lines = ref_content.strip().split('\n')
            print(f"\n总行数: {len(lines)}")
            
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if line:
                    print(f"[{i}] {line}")
            
            # 统计参考文献
            chinese_count, foreign_count = processor._count_references_by_language(ref_content)
            total_count = chinese_count + foreign_count
            
            print(f"\n统计结果:")
            print(f"总条数: {total_count}")
            print(f"中文: {chinese_count}条")
            print(f"外文: {foreign_count}条")
            
            # 关闭文档
            word_instance.close_document()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        resource_manager.shutdown()

if __name__ == "__main__":
    test_detailed_reference_analysis()
