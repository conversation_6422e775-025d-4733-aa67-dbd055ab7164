"""
测试字数统计修复效果
"""

import sys
import os
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app.services.document_processor import DocumentProcessor
    
    def test_word_count_fix():
        """测试字数统计修复效果"""
        
        print("🧪 测试字数统计修复效果")
        print("=" * 60)
        
        # 测试文件路径
        test_file = "D:\\Works\\paper-check-win\\backend\\data\\uploads\\user_2450b8b44a9c4db0842e36cd9e99ed65\\task_cbec6ee040c7434daf0746f053b08286_test.docx"
        
        if not os.path.exists(test_file):
            print(f"❌ 测试文件不存在: {test_file}")
            return False
        
        print(f"✅ 测试文件存在: {test_file}")
        
        try:
            # 创建文档处理器
            processor = DocumentProcessor()
            
            # 分析文档结构
            print("🔍 开始分析文档结构...")
            result = processor.analyze_document_structure(test_file)
            
            if not result or 'document_structures' not in result:
                print("❌ 文档结构分析失败")
                return False
            
            document_structures = result['document_structures']
            print(f"✅ 检测到 {len(document_structures)} 个文档结构")
            
            # 分析字数统计
            print(f"\n📊 字数统计分析:")
            print(f"{'结构名称':<20} {'页码':<6} {'字数':<8} {'类型':<12}")
            print("-" * 50)
            
            total_words = 0
            for structure in document_structures:
                name = structure.get('name', '未知')
                page = structure.get('page', 0)
                word_count = structure.get('word_count', 0)
                struct_type = structure.get('type', 'unknown')
                
                print(f"{name:<20} {page:<6} {word_count:<8} {struct_type:<12}")
                total_words += word_count
            
            print("-" * 50)
            print(f"{'总计':<20} {'':<6} {total_words:<8}")
            
            # 重点检查封面页字数
            cover_structure = None
            for structure in document_structures:
                if structure.get('name') == '封面':
                    cover_structure = structure
                    break
            
            if cover_structure:
                cover_word_count = cover_structure.get('word_count', 0)
                print(f"\n🔍 封面页字数分析:")
                print(f"   检测到的字数: {cover_word_count}")
                
                # 根据封面页内容估算期望字数
                expected_words = len("河北科技学院学士学位论文新媒体技术对舞蹈编导创作手法的影响研究李岩32219350130艺术学院舞蹈编导专升本展烨二〇二五年五月二十日")
                print(f"   期望字数范围: {expected_words-10} - {expected_words+10}")
                
                if cover_word_count >= expected_words - 10:
                    print("   ✅ 封面页字数统计正常")
                    cover_fixed = True
                else:
                    print("   ❌ 封面页字数统计仍然偏低")
                    cover_fixed = False
            else:
                print("   ❌ 未找到封面页结构")
                cover_fixed = False
            
            # 检查其他重要结构
            important_structures = ['任务书', '开题报告', '中文摘要', '正文', '参考文献']
            structure_analysis = {}
            
            for struct_name in important_structures:
                found = False
                for structure in document_structures:
                    if structure.get('name') == struct_name:
                        word_count = structure.get('word_count', 0)
                        structure_analysis[struct_name] = word_count
                        found = True
                        break
                if not found:
                    structure_analysis[struct_name] = 0
            
            print(f"\n📋 重要结构字数分析:")
            for struct_name, word_count in structure_analysis.items():
                status = "✅" if word_count > 0 else "❌"
                print(f"   {status} {struct_name}: {word_count} 字")
            
            # 保存详细结果
            detailed_result = {
                'total_structures': len(document_structures),
                'total_words': total_words,
                'cover_word_count': cover_structure.get('word_count', 0) if cover_structure else 0,
                'structure_analysis': structure_analysis,
                'all_structures': document_structures
            }
            
            with open("word_count_analysis.json", "w", encoding="utf-8") as f:
                json.dump(detailed_result, f, ensure_ascii=False, indent=2, default=str)
            print(f"\n💾 详细分析结果已保存到: word_count_analysis.json")
            
            # 总结修复效果
            print(f"\n📊 修复效果总结:")
            
            if cover_fixed:
                print("   ✅ 封面页字数统计已修复")
            else:
                print("   ❌ 封面页字数统计仍需改进")
            
            structures_with_words = len([s for s in document_structures if s.get('word_count', 0) > 0])
            print(f"   📈 有字数统计的结构: {structures_with_words}/{len(document_structures)}")
            
            if total_words > 1000:  # 一个合理的论文总字数应该超过1000字
                print("   ✅ 总字数统计合理")
            else:
                print("   ❌ 总字数统计偏低")
            
            # 判断修复是否成功
            success = (
                cover_fixed and 
                structures_with_words >= len(document_structures) * 0.7 and  # 至少70%的结构有字数
                total_words > 1000
            )
            
            if success:
                print("\n🎉 字数统计修复成功！")
                return True
            else:
                print("\n❌ 字数统计修复需要进一步改进")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    if __name__ == "__main__":
        success = test_word_count_fix()
        
        if success:
            print("\n🎉 字数统计修复测试成功！")
        else:
            print("\n❌ 字数统计修复测试失败！")
            print("\n🔧 可能的问题:")
            print("   1. 结构检测逻辑需要调整")
            print("   2. 字数统计范围需要优化")
            print("   3. 页面内容提取有问题")

except ImportError as e:
    print(f"❌ 模块导入失败: {str(e)}")
    print("请确保在正确的环境中运行此脚本")
