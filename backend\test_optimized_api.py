"""
测试优化后的API结构
"""

import asyncio
import json
from app.tasks.manager import TaskManager
from app.core.logging import logger


async def test_optimized_api():
    """测试优化后的API结构"""
    
    # 模拟任务数据
    mock_raw_data = {
        'success': True,
        'raw_data': {
            'document_info': {
                'pages': 36,
                'words': 18806,
                'characters': 22630,
                'paragraphs': 514,
                'tables': 5,
                'images': 3,
                'title': 'Test Document',
                'author': 'Test Author',
                'cover_page_info': {
                    'title': 'Test Title',
                    'author': 'Test Author',
                    'major': 'Test Major'
                }
            },
            'document_structure': {
                'document_structures': [
                    {
                        'name': '封面',
                        'type': 'standard',
                        'status': 'present',
                        'page': 1,
                        'word_count': 6,
                        'reference_count': 0,
                        'required': True,
                        'content': {
                            'text': '学士学位论文',
                            'style': '正文',
                            'paragraph_index': 1
                        }
                    },
                    {
                        'name': '测试结构',
                        'type': 'non_standard',
                        'status': 'present',
                        'page': 7,
                        'word_count': 17,
                        'reference_count': 0,
                        'required': False,
                        'content': {
                            'text': '这是一个测试的非标准结构',
                            'style': '标题',
                            'paragraph_index': 10
                        }
                    }
                ],
                'outline': [
                    {
                        'text': '封面',
                        'level': 1,
                        'page': 1,
                        'type': 'standard',
                        'structure_name': '封面'
                    }
                ]
            }
        },
        'processing_time': 2.5
    }
    
    mock_processed_data = {
        'document_info': {
            'processed': True
        },
        'preprocessing_time': 1.0
    }
    
    mock_structure_result = {
        'hierarchy_analysis': {'score': 85},
        'completeness_analysis': {'score': 90},
        'quality_analysis': {'score': 88},
        'document_type': {'type': 'bachelor_thesis'},
        'analysis_time': 1.5
    }
    
    mock_check_result = {
        'compliance_score': 87.5,
        'problems': [
            {'severity': 'error', 'message': 'Test error'},
            {'severity': 'warning', 'message': 'Test warning'}
        ]
    }
    
    # 创建任务管理器实例
    task_manager = TaskManager()
    
    # 测试优化后的结果构建
    logger.info("🧪 测试进一步优化后的API结构构建...")

    final_analysis = task_manager._build_final_analysis_result(
        mock_raw_data,
        mock_processed_data,
        mock_structure_result,
        mock_check_result
    )

    # 模拟完整的任务结果构建
    import time
    task_start_time = time.time()

    # 模拟任务结果构建（简化版）
    document_structures = final_analysis.get('document_structures', [])
    outline = final_analysis.get('outline', [])

    # 构建任务结果
    task_result = {
        "task_type": "paper_check",
        "status": "completed",
        "compliance_score": 87.5,
        "problems_found": 2,
        "processing_time": 2.5,

        # 🔥 新结构：content_stats提升到顶级
        "content_stats": final_analysis.get('statistics', {}),

        "document_structures": document_structures,
        "outline": outline,

        "check_summary": {
            "compliance_score": 87.5,
            "total_problems": 2,
            "major_problems": 1,
            "minor_problems": 1
        },

        # 🔥 只保留一个standard_name
        "detection_standard": "hbkj_bachelor_2024",
        "standard_name": "河北科技学院学士论文检测标准 (2024版)",

        "document_info": {
            "pages": 36,
            "words": 18806,
            "title": "Test Title",
            "author": "Test Author",
            "cover_page_info": {"title": "Test Title", "author": "Test Author"}
        },

        "analysis_summary": final_analysis.get('analysis_summary', {}),
        "processing_meta": final_analysis.get('meta', {})
    }

    # 输出结果
    logger.info("📊 进一步优化后的任务结果结构:")
    print(json.dumps(task_result, indent=2, ensure_ascii=False))

    # 验证关键字段
    assert 'content_stats' in task_result  # 🔥 新位置
    assert 'document_structures' in task_result
    assert 'outline' in task_result
    assert 'check_summary' in task_result
    assert 'analysis_summary' in task_result

    # 验证content_stats包含正确数据
    content_stats = task_result['content_stats']
    assert 'page_count' in content_stats
    assert 'word_count' in content_stats
    assert 'table_count' in content_stats
    assert 'image_count' in content_stats

    # 验证没有analysis_result包装层
    assert 'analysis_result' not in task_result

    # 验证只有一个standard_name
    standard_name_count = str(task_result).count('standard_name')
    assert standard_name_count == 1, f"发现{standard_name_count}个standard_name，应该只有1个"

    logger.info("✅ 进一步优化测试通过！")
    logger.info("🎯 关键改进:")
    logger.info("   - content_stats提升到顶级")
    logger.info("   - 移除analysis_result包装层")
    logger.info("   - 只保留一个standard_name")
    logger.info("   - 扁平化数据结构")


if __name__ == "__main__":
    asyncio.run(test_optimized_api())
