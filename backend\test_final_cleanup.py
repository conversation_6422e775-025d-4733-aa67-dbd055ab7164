"""
最终测试学校和院系字段清理效果
"""

import re


def _clean_department_value(value: str) -> str:
    """清理院系信息中的重复前缀和多余空格"""
    if not value:
        return ''
    
    # 先清理首尾空格和多余空格
    cleaned_value = re.sub(r'\s+', ' ', value.strip())
    
    # 🔥 精确匹配：处理"院 系 XXX"或"院系XXX"格式
    院系_pattern = r'^院\s*系\s*(.+)$'
    match = re.match(院系_pattern, cleaned_value)
    if match:
        cleaned_value = match.group(1).strip()
    
    # 🔥 精确匹配：处理"系 别 XXX"或"系别XXX"格式
    系别_pattern = r'^系\s*别\s*(.+)$'
    match = re.match(系别_pattern, cleaned_value)
    if match:
        cleaned_value = match.group(1).strip()
    
    return cleaned_value


def _clean_school_value(value: str) -> str:
    """清理学校信息中的重复前缀和多余空格"""
    if not value:
        return ''
    
    # 先清理首尾空格和多余空格
    cleaned_value = re.sub(r'\s+', ' ', value.strip())
    
    # 🔥 精确匹配：处理"院 系 XXX"或"院系XXX"格式
    院系_pattern = r'^院\s*系\s*(.+)$'
    match = re.match(院系_pattern, cleaned_value)
    if match:
        cleaned_value = match.group(1).strip()
    
    # 🔥 精确匹配：处理"学 校 XXX"或"学校XXX"格式
    学校_pattern = r'^学\s*校\s*(.+)$'
    match = re.match(学校_pattern, cleaned_value)
    if match:
        cleaned_value = match.group(1).strip()
    
    return cleaned_value


def test_all_cases():
    """测试所有情况"""
    
    print("🧪 最终测试：学校和院系字段清理")
    print("=" * 60)
    
    # 院系测试用例
    department_cases = [
        ("院 系 艺术学院", "艺术学院"),
        ("院系艺术学院", "艺术学院"),
        ("系 别 中文系", "中文系"),
        ("系别中文系", "中文系"),
        ("艺术学院", "艺术学院"),  # 没有前缀
        ("  院 系  艺术学院  ", "艺术学院"),  # 多余空格
        ("", ""),  # 空字符串
    ]
    
    print("📋 院系字段测试:")
    department_passed = 0
    for i, (input_val, expected) in enumerate(department_cases, 1):
        result = _clean_department_value(input_val)
        status = "✅" if result == expected else "❌"
        print(f"   {i}. {status} '{input_val}' -> '{result}' (期望: '{expected}')")
        if result == expected:
            department_passed += 1
    
    # 学校测试用例
    school_cases = [
        ("院 系 艺术学院", "艺术学院"),
        ("院系艺术学院", "艺术学院"),
        ("学 校 河北科技学院", "河北科技学院"),
        ("学校河北科技学院", "河北科技学院"),
        ("河北科技学院", "河北科技学院"),  # 没有前缀
        ("  院 系  艺术学院  ", "艺术学院"),  # 多余空格
        ("", ""),  # 空字符串
    ]
    
    print("\n📋 学校字段测试:")
    school_passed = 0
    for i, (input_val, expected) in enumerate(school_cases, 1):
        result = _clean_school_value(input_val)
        status = "✅" if result == expected else "❌"
        print(f"   {i}. {status} '{input_val}' -> '{result}' (期望: '{expected}')")
        if result == expected:
            school_passed += 1
    
    # 测试原始问题
    print("\n🎯 原始问题测试:")
    original_problem = "院 系 艺术学院"
    department_result = _clean_department_value(original_problem)
    school_result = _clean_school_value(original_problem)
    
    print(f"   原始值: '{original_problem}'")
    print(f"   院系清理后: '{department_result}'")
    print(f"   学校清理后: '{school_result}'")
    
    problem_fixed = (department_result == "艺术学院" and school_result == "艺术学院")
    print(f"   原始问题修复: {'✅' if problem_fixed else '❌'}")
    
    # 总结
    total_department = len(department_cases)
    total_school = len(school_cases)
    
    print(f"\n📊 测试总结:")
    print(f"   院系字段: {department_passed}/{total_department} 通过")
    print(f"   学校字段: {school_passed}/{total_school} 通过")
    print(f"   原始问题: {'✅' if problem_fixed else '❌'}")
    
    all_passed = (department_passed == total_department and 
                  school_passed == total_school and 
                  problem_fixed)
    
    if all_passed:
        print("\n✅ 所有测试通过！")
        print("🎉 学校和院系字段清理修复成功！")
        
        print(f"\n📋 修复效果:")
        print(f"   ✅ 精确匹配前缀，避免误删")
        print(f"   ✅ 处理有空格和无空格的情况")
        print(f"   ✅ 清理多余空格")
        print(f"   ✅ 保持原有有效信息")
        print(f"   ✅ 解决了原始问题 '院 系 艺术学院'")
        
        # 模拟API响应效果
        print(f"\n🎨 API响应效果:")
        print("修复前:")
        print('   "school": "院 系 艺术学院"')
        print('   "department": "院 系 艺术学院"')
        print("\n修复后:")
        print('   "school": "艺术学院"')
        print('   "department": "艺术学院"')
        
    else:
        print("\n❌ 部分测试失败，需要进一步修复")
    
    return all_passed


if __name__ == "__main__":
    test_all_cases()
