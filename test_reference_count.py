#!/usr/bin/env python3
"""
测试参考文献统计逻辑
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.document_processor import DocumentProcessor

def test_reference_counting():
    """测试参考文献计数逻辑"""
    
    # 模拟参考文献文本（基于用户提供的实际数据）
    reference_text = """
[1] 高绿苑."沉浸式"舞蹈剧场中"观演关系研究[D].山东师范大学,2023.

[2] 廖伟.舞台表演视角下新媒体舞蹈的发展策略研究[D].华侨大学,2023.

[3] 郭佩玮,毕思文.论新媒体技术对当代舞蹈创作与传播的影响[J].尚舞,2023,(08):126-128.

[4] 钟晓轩.新媒体视域下的舞蹈变革初探[J].艺术评鉴,2023,(07):61-65.

[5] 王炳臻.数字影像对民族舞蹈创作路径拓展[D].中央民族大学,2023.

[6] 尹紫雯.新媒体舞蹈艺术研究[D].华南理工大学,2022.

[7] 张涵.新媒体视域下舞蹈创作的发展研究[D].天津体育学院,2022.

[8] 赵若伊.浅析舞蹈艺术与新媒体的关系[J].艺术评鉴,2021,(22):177-179.

[9] Donald Hutera.Requardt &amp; Rosenberg: Super Normal Extra Natural[J].The Stage,2025,(10):19.

[10] Georgia Luckhurst.Campaign calls for creativity to be put at heart of curriculum[J].The Stage,2025,5.

[11] Siobhan Murphy.Swan Lake: The Next Generation[J].The Stage,2024,(51):59.

[12] 随朋颖.多媒体技术在舞蹈编导中的应用研究[J].时代报告(奔流),2022,(06):64-66.

[13] 杨子韵.新时代舞蹈编导创作中创新意识的有效体现[J].艺术品鉴,2022,(08):105-107.

[14] 常周妖子.人数关系下的身体技术在编创作品中的运用[D].北京舞蹈学院,2021.
"""

    processor = DocumentProcessor()
    
    print("🔍 测试参考文献统计逻辑")
    print("=" * 50)
    
    # 测试总条数统计
    total_count = processor._count_references_in_text(reference_text)
    print(f"总参考文献条数: {total_count}")
    
    # 测试按语言分类统计
    chinese_count, foreign_count = processor._count_references_by_language(reference_text)
    print(f"中文参考文献: {chinese_count}条")
    print(f"外文参考文献: {foreign_count}条")
    print(f"总计: {chinese_count + foreign_count}条")
    
    print("\n🔍 详细分析:")
    print("-" * 30)
    
    # 分析每一条参考文献
    lines = [line.strip() for line in reference_text.split('\n') if line.strip()]
    
    import re
    reference_patterns = [
        r'^\s*\[\d+\]',  # [1] 格式
        r'^\s*\d+\.\s*',  # 1. 格式  
        r'^\s*\(\d+\)',   # (1) 格式
    ]
    
    processed_numbers = set()
    
    for line in lines:
        # 检查是否是参考文献开始行
        ref_number = None
        for pattern in reference_patterns:
            match = re.match(pattern, line)
            if match:
                # 提取参考文献编号
                number_match = re.search(r'\d+', match.group())
                if number_match:
                    ref_number = int(number_match.group())
                break

        if ref_number is not None and ref_number not in processed_numbers:
            processed_numbers.add(ref_number)
            
            # 判断是中文还是外文参考文献
            is_chinese = processor._is_chinese_reference(line)
            language = "中文" if is_chinese else "外文"
            
            print(f"[{ref_number}] {language}: {line[:50]}...")
    
    print("\n✅ 期望结果:")
    print("- 总条数应该是 14 条（不是15条）")
    print("- 中文参考文献应该是 11 条")
    print("- 外文参考文献应该是 3 条")
    print("- 显示格式应该是: '中文11条;外文3条'")

if __name__ == "__main__":
    test_reference_counting()
