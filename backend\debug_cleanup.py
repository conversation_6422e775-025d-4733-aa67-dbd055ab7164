"""
调试清理函数
"""

import re


def debug_clean_department_value(value: str) -> str:
    """调试版本的清理函数"""
    print(f"输入: '{value}'")
    
    if not value:
        return ''
    
    # 先清理首尾空格
    cleaned_value = value.strip()
    print(f"清理首尾空格后: '{cleaned_value}'")
    
    # 移除常见的重复前缀（只在开头匹配）
    prefixes_to_remove = [
        r'^院\s*系\s+',  # 移除开头的"院 系 "（后面必须有空格）
        r'^系\s*别\s+',  # 移除开头的"系 别 "（后面必须有空格）
    ]
    
    for prefix_pattern in prefixes_to_remove:
        old_value = cleaned_value
        cleaned_value = re.sub(prefix_pattern, '', cleaned_value)
        if old_value != cleaned_value:
            print(f"应用模式 '{prefix_pattern}' 后: '{cleaned_value}'")
    
    # 清理多余的空格
    old_value = cleaned_value
    cleaned_value = re.sub(r'\s+', ' ', cleaned_value).strip()
    if old_value != cleaned_value:
        print(f"清理多余空格后: '{cleaned_value}'")
    
    # 🔥 额外处理：如果还有前缀残留，再次清理
    if cleaned_value.startswith('院 系') or cleaned_value.startswith('系 别'):
        print(f"检测到前缀残留: '{cleaned_value}'")
        # 找到第一个非前缀的内容
        parts = cleaned_value.split()
        print(f"分割后的部分: {parts}")
        if len(parts) >= 3:  # 至少有"院 系 学院名"三部分
            cleaned_value = ' '.join(parts[2:])  # 取第三部分开始的内容
            print(f"取第三部分后: '{cleaned_value}'")
    
    print(f"最终结果: '{cleaned_value}'")
    print("-" * 40)
    return cleaned_value


# 测试问题用例
test_case = '  院 系  艺术学院  '
print(f"测试用例: '{test_case}'")
result = debug_clean_department_value(test_case)
print(f"期望: '艺术学院'")
print(f"实际: '{result}'")
print(f"匹配: {result == '艺术学院'}")
