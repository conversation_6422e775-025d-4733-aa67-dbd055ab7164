{"total_structures": 19, "total_words": 2357, "cover_word_count": 61, "structure_analysis": {"任务书": 58, "开题报告": 19, "中文摘要": 360, "正文": 657, "参考文献": 313}, "all_structures": [{"name": "封面", "status": "present", "type": "standard", "page": 1, "content": {"text": "学士学位论文", "paragraph_index": 1, "alignment": "center", "font_size": 36.0, "style": "正文", "is_bold": false, "level": 0, "structure_name": "封面"}, "identifiers_matched": ["学士学位论文", "题目", "学生", "姓名", "指导教师"], "required": true, "word_count": 61, "reference_count": 0, "level": 0}, {"name": "任务书", "status": "present", "type": "standard", "page": 2, "content": {"text": "河北科技学院本科生毕业设计（论文）任务书", "paragraph_index": 0, "alignment": "center", "font_size": 15.0, "style": "任务书-标题", "is_bold": false, "level": 1, "structure_name": "任务书"}, "identifiers_matched": ["任务书", "毕业设计任务书"], "required": true, "word_count": 58, "reference_count": 0, "level": 1}, {"name": "开题报告", "status": "present", "type": "standard", "page": 3, "content": {"text": "河北科技学院本科生毕业设计(论文)开题报告", "paragraph_index": 0, "alignment": "center", "font_size": 15.0, "style": "开题报告-标题", "is_bold": false, "level": 1, "structure_name": "开题报告"}, "identifiers_matched": ["开题报告"], "required": true, "word_count": 19, "reference_count": 0, "level": 1}, {"name": "河北科技学院本科生毕业设计（论文）测试", "status": "present", "type": "non_standard", "page": 7, "content": {"text": "河北科技学院本科生毕业设计（论文）测试", "paragraph_index": 0, "alignment": "center", "font_size": 15.0, "style": "任务书-标题", "is_bold": false, "level": 2, "structure_name": "河北科技学院本科生毕业设计（论文）测试"}, "identifiers_matched": [], "required": false, "word_count": 17, "reference_count": 0, "level": 2}, {"name": "诚信声明", "status": "present", "type": "standard", "page": 9, "content": {"text": "学位论文原创性声明", "paragraph_index": 0, "alignment": "center", "font_size": 15.0, "style": "标题 1- 非目录", "is_bold": false, "level": 1, "structure_name": "诚信声明"}, "identifiers_matched": ["诚信声明", "承诺书", "原创性声明"], "required": true, "word_count": 325, "reference_count": 0, "level": 1}, {"name": "版权声明", "status": "present", "type": "standard", "page": 9, "content": {"text": "学位论文版权使用授权书", "paragraph_index": 5, "alignment": "center", "font_size": 15.0, "style": "标题 1- 非目录", "is_bold": false, "level": 1, "structure_name": "版权声明"}, "identifiers_matched": ["版权声明", "版权", "知识产权", "使用授权书", "学位论文版权使用授权书"], "required": true, "word_count": 325, "reference_count": 0, "level": 1}, {"name": "河北科技学院本科生毕业设计（论文）测试2", "status": "present", "type": "non_standard", "page": 10, "content": {"text": "河北科技学院本科生毕业设计（论文）测试2", "paragraph_index": 0, "alignment": "center", "font_size": 15.0, "style": "任务书-标题", "is_bold": false, "level": 2, "structure_name": "河北科技学院本科生毕业设计（论文）测试2"}, "identifiers_matched": [], "required": false, "word_count": 17, "reference_count": 0, "level": 2}, {"name": "中文摘要", "status": "present", "type": "standard", "page": 11, "content": {"text": "摘　　要", "paragraph_index": 0, "alignment": "center", "font_size": 15.0, "style": "标题 1- 非目录", "is_bold": false, "level": 1, "structure_name": "中文摘要"}, "identifiers_matched": ["摘要", "概要"], "required": true, "word_count": 360, "reference_count": 0, "level": 1}, {"name": "中文关键词", "status": "present", "type": "standard", "page": 11, "content": {"text": "关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）；短视频传播；编导创作手法", "paragraph_index": 2, "alignment": "justify", "font_size": 12.0, "style": "正文", "is_bold": false, "level": 2, "structure_name": "中文关键词"}, "identifiers_matched": ["关键词", "关键字"], "required": true, "level": 2}, {"name": "英文摘要", "status": "present", "type": "standard", "page": 12, "content": {"text": "ABSTRACT", "paragraph_index": 0, "alignment": "center", "font_size": 15.0, "style": "标题 1- 非目录", "is_bold": false, "level": 1, "structure_name": "英文摘要"}, "identifiers_matched": ["abstract"], "required": true, "word_count": 195, "reference_count": 0, "level": 1}, {"name": "英文关键词", "status": "present", "type": "standard", "page": 12, "content": {"text": "Key words:dance creation;virtual reality (VR); augmented reality (AR); short video communication; choreography and creation techniques", "paragraph_index": 2, "alignment": "justify", "font_size": 12.0, "style": "正文", "is_bold": true, "level": 2, "structure_name": "英文关键词"}, "identifiers_matched": ["keywords", "key words"], "required": true, "level": 2}, {"name": "目录", "status": "present", "type": "standard", "page": 13, "content": {"text": "目　　录", "paragraph_index": 0, "alignment": "center", "font_size": 15.0, "style": "标题 1- 非目录", "is_bold": false, "toc_entries_count": 56, "detection_method": "toc_style", "level": 1, "structure_name": "目录"}, "identifiers_matched": ["目录", "contents"], "required": true, "level": 1}, {"name": "正文", "status": "present", "type": "standard", "page": 15, "content": {"text": "绪论", "paragraph_index": 0, "alignment": "center", "font_size": 15.0, "style": "标题 1", "is_bold": false, "level": 1, "structure_name": "正文"}, "identifiers_matched": ["第", "章", "引言", "绪论"], "required": true, "word_count": 657, "reference_count": 0, "level": 1}, {"name": "结　　论", "status": "present", "type": "non_standard", "page": 32, "content": {"text": "结　　论", "paragraph_index": 0, "alignment": "center", "font_size": 15.0, "style": "标题1 - 目录 - 非编号", "is_bold": false, "level": 2, "structure_name": "结　　论"}, "identifiers_matched": [], "required": false, "word_count": 2, "reference_count": 0, "level": 2}, {"name": "参考文献", "status": "present", "type": "standard", "page": 33, "content": {"text": "参考文献", "paragraph_index": 0, "alignment": "center", "font_size": 15.0, "style": "标题1 - 目录 - 非编号", "is_bold": false, "level": 1, "structure_name": "参考文献"}, "identifiers_matched": ["参考文献", "reference", "参考资料"], "required": true, "word_count": 313, "reference_count": 15, "level": 1}, {"name": "列表啊", "status": "present", "type": "non_standard", "page": 34, "content": {"text": "列表啊", "paragraph_index": 0, "alignment": "center", "font_size": 15.0, "style": "标题1 - 目录 - 非编号", "is_bold": false, "level": 2, "structure_name": "列表啊"}, "identifiers_matched": [], "required": false, "word_count": 3, "reference_count": 0, "level": 2}, {"name": "致谢", "status": "present", "type": "standard", "page": 35, "content": {"text": "致　　谢", "paragraph_index": 0, "alignment": "center", "font_size": 15.0, "style": "标题1 - 目录 - 非编号", "is_bold": false, "level": 1, "structure_name": "致谢"}, "identifiers_matched": ["致谢", "谢辞"], "required": true, "word_count": 2, "reference_count": 0, "level": 1}, {"name": "列表啊2", "status": "present", "type": "non_standard", "page": 36, "content": {"text": "列表啊2", "paragraph_index": 0, "alignment": "center", "font_size": 15.0, "style": "标题1 - 目录 - 非编号", "is_bold": false, "level": 2, "structure_name": "列表啊2"}, "identifiers_matched": [], "required": false, "word_count": 3, "reference_count": 0, "level": 2}, {"name": "附录", "status": "missing", "type": "standard", "required": true, "word_count": 0, "reference_count": 0, "level": 1, "content": {"level": 1, "structure_name": "附录", "font_size": null, "is_bold": false, "alignment": "left"}}]}