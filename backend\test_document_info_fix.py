"""
测试文档信息修复效果
"""

import json


def test_document_info_extraction():
    """测试文档信息提取效果"""
    
    print("🧪 测试文档信息修复效果...")
    
    # 模拟优化后的API响应结构
    mock_api_response = {
        "success": True,
        "code": 200,
        "message": "获取任务信息成功",
        "data": {
            "task_id": "task_test123",
            "filename": "test.docx",
            "file_size": 2560385,
            "analysis_options": {
                "detection_standard": "hbkj_bachelor_2024"
            },
            "result": {
                # 🔥 优化后的扁平化document_info结构
                "document_info": {
                    "title": "新媒体技术对舞蹈编导创作手法的影响研究",
                    "author": "李岩",
                    "major": "舞蹈编导（专升本）",
                    "department": "艺术学院",
                    "student_id": "32219350130",
                    "advisor": "展烨",
                    "date": "2025年5月20日",
                    "degree_type": "学士学位论文",
                    "school": "院 系 艺术学院"
                },
                "content_stats": {
                    "page_count": 36,
                    "word_count": 18806,
                    "table_count": 10,
                    "image_count": 9,
                    "paragraph_count": 514
                },
                "document_structures": [
                    {
                        "name": "封面",
                        "type": "standard",
                        "level": 0,
                        "page": 1,
                        "status": "present"
                    }
                ],
                "standard_name": "河北科技学院学士论文检测标准 (2024版)",
                "compliance_score": 87.5
            },
            "created_at": "2025-07-26T16:36:21.755181+00:00",
            "status": "completed"
        }
    }
    
    print("📊 API响应结构分析:")
    task_result = mock_api_response["data"]["result"]
    document_info = task_result.get("document_info", {})
    
    print(f"   包含document_info: {'document_info' in task_result}")
    print(f"   document_info字段数: {len(document_info)}")
    print(f"   document_info结构: {list(document_info.keys())}")
    
    # 模拟前端数据提取逻辑（修复后）
    print("\n🎯 模拟前端数据提取（修复后）:")
    
    def get_document_title():
        # 🔥 修复：使用扁平化结构
        if task_result.get("document_info", {}).get("title"):
            return task_result["document_info"]["title"]
        return "暂无标题"
    
    def get_author():
        # 🔥 修复：使用扁平化结构
        if task_result.get("document_info", {}).get("author"):
            return task_result["document_info"]["author"]
        return "暂无作者信息"
    
    def get_major():
        # 🔥 修复：使用扁平化结构
        if task_result.get("document_info", {}).get("major"):
            return task_result["document_info"]["major"]
        return "暂无专业信息"
    
    def get_student_id():
        # 🔥 修复：使用扁平化结构
        if task_result.get("document_info", {}).get("student_id"):
            return task_result["document_info"]["student_id"]
        return "暂无学号"
    
    def get_department():
        # 🔥 修复：使用扁平化结构
        if task_result.get("document_info", {}).get("department"):
            return task_result["document_info"]["department"]
        return "暂无院系信息"
    
    def get_advisor():
        # 🔥 修复：使用扁平化结构
        if task_result.get("document_info", {}).get("advisor"):
            return task_result["document_info"]["advisor"]
        return "暂无指导老师信息"
    
    def get_paper_date():
        # 🔥 修复：使用扁平化结构
        if task_result.get("document_info", {}).get("date"):
            return task_result["document_info"]["date"]
        return "暂无时间信息"
    
    # 提取所有文档信息
    extracted_info = {
        "title": get_document_title(),
        "author": get_author(),
        "major": get_major(),
        "student_id": get_student_id(),
        "department": get_department(),
        "advisor": get_advisor(),
        "date": get_paper_date()
    }
    
    print("📋 提取的文档信息:")
    for key, value in extracted_info.items():
        status = "✅" if not value.startswith("暂无") else "❌"
        print(f"   {status} {key}: {value}")
    
    # 验证修复效果
    print("\n✅ 验证修复效果:")
    
    # 验证所有字段都能正确提取
    assert extracted_info["title"] == "新媒体技术对舞蹈编导创作手法的影响研究", f"❌ 标题提取错误: {extracted_info['title']}"
    assert extracted_info["author"] == "李岩", f"❌ 作者提取错误: {extracted_info['author']}"
    assert extracted_info["major"] == "舞蹈编导（专升本）", f"❌ 专业提取错误: {extracted_info['major']}"
    assert extracted_info["student_id"] == "32219350130", f"❌ 学号提取错误: {extracted_info['student_id']}"
    assert extracted_info["department"] == "艺术学院", f"❌ 院系提取错误: {extracted_info['department']}"
    assert extracted_info["advisor"] == "展烨", f"❌ 指导老师提取错误: {extracted_info['advisor']}"
    assert extracted_info["date"] == "2025年5月20日", f"❌ 日期提取错误: {extracted_info['date']}"
    
    print("   ✅ 所有文档信息字段提取正确")
    
    # 验证没有"暂无"信息
    no_data_count = sum(1 for value in extracted_info.values() if value.startswith("暂无"))
    assert no_data_count == 0, f"❌ 仍有{no_data_count}个字段显示'暂无信息'"
    
    print("   ✅ 没有'暂无信息'的字段")
    
    # 模拟前端显示效果
    print("\n🎨 模拟前端显示效果:")
    print("=" * 50)
    print("📄 文档信息")
    print("=" * 50)
    print(f"📝 论文题目")
    print(f"   {extracted_info['title']}")
    print()
    print(f"👤 作者: {extracted_info['author']}")
    print(f"🎓 学号: {extracted_info['student_id']}")
    print(f"🏫 院系: {extracted_info['department']}")
    print()
    print(f"📚 专业: {extracted_info['major']}")
    print(f"👨‍🏫 指导老师: {extracted_info['advisor']}")
    print(f"📅 论文时间: {extracted_info['date']}")
    print("=" * 50)
    
    # 对比修复前后
    print("\n📊 修复前后对比:")
    print("修复前:")
    print("   ❌ 论文题目: 暂无作者信息")
    print("   ❌ 作者: 暂无作者信息") 
    print("   ❌ 学号: 暂无学号")
    print("   ❌ 院系: 暂无院系信息")
    print("   ❌ 专业: 暂无专业信息")
    print("   ❌ 指导老师: 暂无指导老师信息")
    print("   ❌ 论文时间: 暂无时间信息")
    
    print("\n修复后:")
    print("   ✅ 论文题目: 新媒体技术对舞蹈编导创作手法的影响研究")
    print("   ✅ 作者: 李岩")
    print("   ✅ 学号: 32219350130")
    print("   ✅ 院系: 艺术学院")
    print("   ✅ 专业: 舞蹈编导（专升本）")
    print("   ✅ 指导老师: 展烨")
    print("   ✅ 论文时间: 2025年5月20日")
    
    print("\n✅ 所有测试通过！")
    print("🎉 文档信息修复成功！")
    
    print("\n📊 修复效果总结:")
    print("   ✅ 修复了文档信息获取路径")
    print("   ✅ 适配了扁平化的document_info结构")
    print("   ✅ 所有字段都能正确显示")
    print("   ✅ 消除了'暂无信息'的显示")
    print("   ✅ 提升了用户体验")


if __name__ == "__main__":
    test_document_info_extraction()
